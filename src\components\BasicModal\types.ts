export interface IModalProps {
  visible?: boolean
  confirmLoading?: boolean
  cancelLoading?: boolean
  footerVisible?: boolean
}

export interface IModalMethods {
  setModalProps: (props: IModalProps) => void
  emitVisible?: (visible: boolean, uid: number) => void
}

export type RegisterFn = (modalMethods: IModalMethods, uuid: number) => void

export interface IReturnMethods {
  openModal: <T>(options?: IOpenModalOptions<T>) => void
  closeModal: (options?: ICloseModalOptions) => void
  setModalProps?: (props: IModalProps) => void
}

/* useModal hooks 函数返回值类型 */
export type UseModalReturnType = [RegisterFn, IReturnMethods]

export interface IModalOptions {
  confirmCallback?: () => Promise<any>
}

export interface IOpenModalOptions<T> {
  data?: T
  beforeOpen?: () => void
  afterOpen?: () => void
  beforeClose?: () => void
}

export type CallbackFnType = <T = any>(...args: T[]) => void

export interface IOpenModalInnerOptions {
  beforeOpen?: () => void
  afterOpen?: () => void
  beforeClose?: () => void
}

export interface ICloseModalOptions {
  beforeClose?: () => void
}

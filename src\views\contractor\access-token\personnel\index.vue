<template>
  <div class="access-person-token">
    <Table ref="tableRef" :type="0" v-bind="tableConfig" @create-card="handleCreateCard" />
    <EditModal ref="editModalRef" :type="0" @register="register" @submit:success="tableRef?.tableInstance?.refresh" />
    <PrintCardModal @register="registerCardModal">
      <div class="card-content">
        <div class="info">
          <p>姓名：{{ rowData.username }}</p>
          <p>性别：{{ rowData.sex }}</p>
          <p>单位：{{ rowData.company }}</p>
          <p>项目：{{ rowData.projectName }}</p>
        </div>
        <div class="avatar">
          <img :src="rowData.avatar" :alt="rowData.avatar" crossorigin="anonymous" />
        </div>
      </div>
      <p class="period">{{ rowData.startTime }}-{{ rowData.endTime }}</p>
      <template #back>
        <div class="card-content back">
          <p>1. 本证只限有效期间使用,过期作废。</p>
          <p>2. 本职使用完毕,应立即归还发证部门。</p>
          <p>3. 本证只限本人工作佩用,不得转借他人或涂改姓名。</p>
          <p>4. 本证如遇遗失,应立即报发证部门补发</p>
          <p>5. 本证只限于出入本厂大门及工作配用,不做其他任何。</p>
        </div>
      </template>
    </PrintCardModal>
  </div>
</template>

<script setup lang="ts">
import Table from '../components/Table.vue'
import type { ITableSchema } from '@/components/BasicTable/types'
import EditModal from '../components/EditModal.vue'
import { useModal } from '@/components/BasicModal/hooks/useModal'
import { DICT_TYPE } from '@/utils/dict'
import PrintCardModal from '../components/PrintCardModal.vue'
import { getAccessToken } from '@/utils/auth'

defineOptions({
  name: 'ConAccessPersonnelToken'
})

interface Row {
  username: string
  sex: number
  carNumber: string
  avatar: string
  company: string
  projectName: string
  projectComeName: string
  startTime: string
  endTime: string
  status: number
  type: number
}

const [register, { openModal }] = useModal()
const [registerCardModal, { openModal: openCardModal }] = useModal()

/* 编辑对话框组件实例 */
const editModalRef = ref<Nullable<InstanceType<typeof EditModal>>>(null)

/* 表格组件实例 */
const tableRef = ref<Nullable<InstanceType<typeof Table>>>(null)

/** 表格含数据 */
const rowData = ref()

const tableConfig: ITableSchema = {
  columns: [
    {
      label: '姓名',
      prop: 'username',
      enableSearch: true,
      searchFormItemProps: {
        placeholder: '请输入用户名'
      }
    },
    {
      label: '性别',
      prop: 'sex',
      dictTag: true,
      dictType: DICT_TYPE.SYSTEM_USER_SEX
    }
  ],
  toolbar: [
    {
      name: 'add',
      text: '新增',
      type: 'primary',
      icon: 'ep:plus',
      onClick() {
        openModal({
          beforeOpen: () => {
            editModalRef.value?.resetFields()
          }
        })
      }
    }
  ]
}

/** 生成临时出入证 */
const handleCreateCard = (row: Row) => {
  if (!row) return
  const baseURL = import.meta.env.VITE_BASE_URL
  const avatar = row.avatar.match(/(\/\w+)\.jpg|png|jpeg|gif|webp$/)?.[0]
  row.avatar = new URL(
    `/admin-api/system/user/download${avatar}?token=${getAccessToken()}&r=${Date.now()}`,
    baseURL
  ).href
  console.log(row.avatar)
  rowData.value = row
  openCardModal({
    data: row
  })
}
</script>

<style lang="scss" scoped></style>

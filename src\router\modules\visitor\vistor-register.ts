import { Layout } from '@/utils/routerHelper'
import { t } from '@/hooks/web/useI18n'

const visitorRegisterRoute: AppRouteRecordRaw = {
  path: '/visitor-register',
  name: '',
  component: Layout,
  redirect: '/visitor-register/index',
  meta: {
    hidden: true,
    title: t('router.visitor.visitorRegister'),
    systemType: 2,
    noCache: true
  },
  children: [
    {
      path: 'index',
      name: 'VisitorRegister',
      component: () => import('@/views/visitor/register/index.vue'),
      meta: {
        title: t('router.visitor.visitorRegister'),
        systemType: 2,
        noCache: true
      }
    }
  ]
}

export default visitorRegisterRoute

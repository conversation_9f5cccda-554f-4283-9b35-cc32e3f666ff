<template>
  <div class="card-list">
    <div class="card-list-wrap">
      <div class="card-list-item" v-for="item in list" :key="item.id">
        <CardItem :cardItemField="props.listConfig.cardItemField" :data="item" :actions="props.listConfig.actions">
          <template #desc="scoped">
            <slot name="desc" v-bind="scoped"></slot>
          </template>
        </CardItem>
      </div>
    </div>
    <Pagination
      class="pagination"
      ref="paginationRef"
      size="small"
      v-model:page="pagination.page"
      v-model:limit="pagination.limit"
      :total="pagination.total"
      @pagination="getList"
    />
  </div>
</template>

<script setup lang="ts">
import CardItem from './components/CardItem.vue'
import { ICardListConf } from '../types'
import Pagination from '@/components/Pagination/index.vue'

defineOptions({
  name: 'CardList'
})

const paginationRef = ref<InstanceType<typeof Pagination>>()

const props = defineProps<{
  listConfig: ICardListConf
}>()

/** 分页 */
const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

/** 列表数据 */
const list = ref<any[]>([])

/** 获取数据 */
function getList() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.limit
  }
  props.listConfig
    .apiFn?.(params)
    .then((res) => {
      console.log(res)
      list.value = res.list
      pagination.total = res.total
    })
    .catch((err) => {
      console.log(err)
    })
    .finally(() => {})
}

/** 刷新数据 */
function refresh() {
  pagination.page = 0
  pagination.limit = 10
  getList()
}

onMounted(() => {
  getList()
})

defineExpose({
  refresh
})
</script>

<style lang="scss" scoped>
.card-list {
  height: 100%;

  &-wrap {
    display: grid;
    height: calc(100% - 54px);
    overflow: auto;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    grid-template-rows: repeat(auto-fill, 200px);
    gap: 10px;
  }
}
</style>

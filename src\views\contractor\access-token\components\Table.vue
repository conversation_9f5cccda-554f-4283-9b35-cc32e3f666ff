<template>
  <div class="access-token-table">
    <BasicTable ref="basicTableRef" :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'
import { queryAccessTokenAPI } from '@/api/contractor/access-token'
import { getSimpleDeptList } from '@/api/system/dept'
import { projectListAPI } from '@/api/contractor/project'
import { DICT_TYPE } from '@/utils/dict'

defineOptions({
  name: 'AccessTokenTable'
})

const basicTableRef = ref<Nullable<IBasicTableExpose>>(null)

const props = defineProps<{
  type: number
  toolbar?: ITableSchema['toolbar']
  columns: ITableSchema['columns']
}>()

const emit = defineEmits<{
  (e: 'create-card', row: any)
}>()

const tableConfig: ITableSchema = {
  beforeFetch() {
    return {
      type: props.type
    }
  },
  apiFn: queryAccessTokenAPI,
  columns: [
    ...props.columns,
    {
      label: '单位',
      prop: 'company',
      enableSearch: true
    },
    {
      label: '项目',
      prop: 'projectName',
      enableSearch: true,
      searchFormItemProps: {
        field: 'projectId',
        component: 'Select',
        componentProps: {
          optionsLabelField: 'projectName'
        },
        options: projectListAPI.bind(null, { pageNo: 1, pageSize: 1000 })
      }
    },
    {
      label: '项目来源',
      prop: 'projectComeName'
    },
    {
      label: '开始时间',
      prop: 'startTime',
      width: 220
    },
    {
      label: '结束时间',
      prop: 'endTime',
      width: 220
    },
    {
      label: '状态',
      prop: 'status',
      dictTag: true,
      dictType: DICT_TYPE.CONTRACTOR_TEMPRORARY_STATUS
    }
  ],

  toolbar: props.toolbar,
  showActions: true,
  actionsColumn: {
    actions: [
      {
        name: 'add',
        text: '临时牌生成',
        permission: ['contractor:access:add'],
        onClick({ row }) {
          console.log(row)
          emit('create-card', row)
        }
      }
    ]
  }
}

defineExpose({
  tableInstance: basicTableRef
})
</script>

<style lang="scss" scoped></style>

import { PropType, ExtractPropTypes } from 'vue'
import { buttonTypes } from 'element-plus'

export const buttonProps = {
  type: {
    type: String as PropType<(typeof buttonTypes)[number]>,
    values: buttonTypes,
    default: 'primary'
  },
  icon: {
    type: String
  },
  plain: {
    type: Boolean,
    default: undefined
  }
}

export type ButtonProps = ExtractPropTypes<typeof buttonProps> & {
  [key in string]: any
}

export interface ButtonEmits {
  click(): void
}

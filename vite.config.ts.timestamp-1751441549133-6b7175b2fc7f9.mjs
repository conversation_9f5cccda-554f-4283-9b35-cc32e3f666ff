// vite.config.ts
import { resolve as resolve2 } from "path";
import { loadEnv } from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0/node_modules/vite/dist/node/index.js";

// build/vite/index.ts
import { resolve } from "path";
import Vue from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/@vitejs+plugin-vue@5.2.1_vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0__vue@3.5.12_typescript@5.3.3_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import VueJsx from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/@vitejs+plugin-vue-jsx@3.1.0_vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0__vue@3.5.12_typescript@5.3.3_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import progress from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/vite-plugin-progress@0.0.7_vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0_/node_modules/vite-plugin-progress/dist/index.mjs";
import EslintPlugin from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/vite-plugin-eslint@1.8.1_eslint@8.57.1_vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0_/node_modules/vite-plugin-eslint/dist/index.mjs";
import { ViteEjsPlugin } from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/vite-plugin-ejs@1.7.0_vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0_/node_modules/vite-plugin-ejs/index.js";
import ElementPlus from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/unplugin-element-plus@0.8.0_rollup@4.27.4/node_modules/unplugin-element-plus/dist/vite.mjs";
import AutoImport from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/unplugin-auto-import@0.16.7_@vueuse+core@10.11.1_vue@3.5.12_typescript@5.3.3___rollup@4.27.4/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/unplugin-vue-components@0.25.2_@babel+parser@7.26.2_rollup@4.27.4_vue@3.5.12_typescript@5.3.3_/node_modules/unplugin-vue-components/dist/vite.mjs";
import { ElementPlusResolver } from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/unplugin-vue-components@0.25.2_@babel+parser@7.26.2_rollup@4.27.4_vue@3.5.12_typescript@5.3.3_/node_modules/unplugin-vue-components/dist/resolvers.mjs";
import viteCompression from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/vite-plugin-compression@0.5.1_vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0_/node_modules/vite-plugin-compression/dist/index.mjs";
import topLevelAwait from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/vite-plugin-top-level-await@1.4.4_rollup@4.27.4_vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0_/node_modules/vite-plugin-top-level-await/exports/import.mjs";
import VueI18nPlugin from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/@intlify+unplugin-vue-i18n@2.0.0_rollup@4.27.4_vue-i18n@9.10.2_vue@3.5.12_typescript@5.3.3__/node_modules/@intlify/unplugin-vue-i18n/lib/vite.mjs";
import { createSvgIconsPlugin } from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0_/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import UnoCSS from "file:///E:/ZDD/Desktop/%E9%97%A8%E7%A6%81/hdx-entrance-guard/node_modules/.pnpm/unocss@0.58.9_postcss@8.4.49_rollup@4.27.4_vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0_/node_modules/unocss/dist/vite.mjs";
var __vite_injected_original_dirname = "E:\\ZDD\\Desktop\\\u95E8\u7981\\hdx-entrance-guard\\build\\vite";
function createVitePlugins() {
  const root2 = process.cwd();
  function pathResolve2(dir) {
    return resolve(root2, ".", dir);
  }
  return [
    Vue(),
    VueJsx(),
    UnoCSS(),
    progress(),
    // PurgeIcons(),
    ElementPlus({}),
    AutoImport({
      include: [
        /\.[tj]sx?$/,
        // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/,
        // .vue
        /\.md$/
        // .md
      ],
      imports: [
        "vue",
        "vue-router",
        // 可额外添加需要 autoImport 的组件
        {
          "@/hooks/web/useI18n": ["useI18n"],
          "@/hooks/web/useMessage": ["useMessage"],
          "@/hooks/web/useTable": ["useTable"],
          "@/hooks/web/useCrudSchemas": ["useCrudSchemas"],
          "@/utils/formRules": ["required"],
          "@/utils/dict": ["DICT_TYPE"]
        }
      ],
      dts: "src/types/auto-imports.d.ts",
      resolvers: [ElementPlusResolver()],
      eslintrc: {
        enabled: false,
        // Default `false`
        filepath: "./.eslintrc-auto-import.json",
        // Default `./.eslintrc-auto-import.json`
        globalsPropValue: true
        // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
      }
    }),
    Components({
      // 生成自定义 `auto-components.d.ts` 全局声明
      dts: "src/types/auto-components.d.ts",
      // 自定义组件的解析器
      resolvers: [ElementPlusResolver()],
      globs: ["src/components/**/**.{vue, md}", "!src/components/DiyEditor/components/mobile/**"]
    }),
    EslintPlugin({
      cache: false,
      include: ["src/**/*.vue", "src/**/*.ts", "src/**/*.tsx"]
      // 检查的文件
    }),
    VueI18nPlugin({
      runtimeOnly: true,
      compositionOnly: true,
      include: [resolve(__vite_injected_original_dirname, "src/locales/**")]
    }),
    createSvgIconsPlugin({
      iconDirs: [pathResolve2("src/assets/svgs")],
      symbolId: "icon-[dir]-[name]",
      svgoOptions: true
    }),
    viteCompression({
      verbose: true,
      // 是否在控制台输出压缩结果
      disable: false,
      // 是否禁用
      threshold: 10240,
      // 体积大于 threshold 才会被压缩,单位 b
      algorithm: "gzip",
      // 压缩算法,可选 [ 'gzip' , 'brotliCompress' ,'deflate' , 'deflateRaw']
      ext: ".gz",
      // 生成的压缩包后缀
      deleteOriginFile: false
      //压缩后是否删除源文件
    }),
    ViteEjsPlugin(),
    topLevelAwait({
      // https://juejin.cn/post/7152191742513512485
      // The export name of top-level await promise for each chunk module
      promiseExportName: "__tla",
      // The function to generate import names of top-level await promise in each chunk module
      promiseImportName: (i) => `__tla_${i}`
    })
  ];
}

// build/vite/optimize.ts
var include = [
  "qs",
  "url",
  "vue",
  "sass",
  "mitt",
  "axios",
  "pinia",
  "dayjs",
  "qrcode",
  "unocss",
  "vue-router",
  "vue-types",
  "vue-i18n",
  "crypto-js",
  "cropperjs",
  "lodash-es",
  "nprogress",
  "web-storage-cache",
  "@iconify/iconify",
  "@vueuse/core",
  "@zxcvbn-ts/core",
  "echarts/core",
  "echarts/charts",
  "echarts/components",
  "echarts/renderers",
  "echarts-wordcloud",
  "@wangeditor/editor",
  "@wangeditor/editor-for-vue",
  "@microsoft/fetch-event-source",
  "markdown-it",
  "markmap-view",
  "markmap-lib",
  "markmap-toolbar",
  "highlight.js",
  "element-plus",
  "element-plus/es",
  "element-plus/es/locale/lang/zh-cn",
  "element-plus/es/locale/lang/en",
  "element-plus/es/components/avatar/style/css",
  "element-plus/es/components/space/style/css",
  "element-plus/es/components/backtop/style/css",
  "element-plus/es/components/form/style/css",
  "element-plus/es/components/radio-group/style/css",
  "element-plus/es/components/radio/style/css",
  "element-plus/es/components/checkbox/style/css",
  "element-plus/es/components/checkbox-group/style/css",
  "element-plus/es/components/switch/style/css",
  "element-plus/es/components/time-picker/style/css",
  "element-plus/es/components/date-picker/style/css",
  "element-plus/es/components/descriptions/style/css",
  "element-plus/es/components/descriptions-item/style/css",
  "element-plus/es/components/link/style/css",
  "element-plus/es/components/tooltip/style/css",
  "element-plus/es/components/drawer/style/css",
  "element-plus/es/components/dialog/style/css",
  "element-plus/es/components/checkbox-button/style/css",
  "element-plus/es/components/option-group/style/css",
  "element-plus/es/components/radio-button/style/css",
  "element-plus/es/components/cascader/style/css",
  "element-plus/es/components/color-picker/style/css",
  "element-plus/es/components/input-number/style/css",
  "element-plus/es/components/rate/style/css",
  "element-plus/es/components/select-v2/style/css",
  "element-plus/es/components/tree-select/style/css",
  "element-plus/es/components/slider/style/css",
  "element-plus/es/components/time-select/style/css",
  "element-plus/es/components/autocomplete/style/css",
  "element-plus/es/components/image-viewer/style/css",
  "element-plus/es/components/upload/style/css",
  "element-plus/es/components/col/style/css",
  "element-plus/es/components/form-item/style/css",
  "element-plus/es/components/alert/style/css",
  "element-plus/es/components/breadcrumb/style/css",
  "element-plus/es/components/select/style/css",
  "element-plus/es/components/input/style/css",
  "element-plus/es/components/breadcrumb-item/style/css",
  "element-plus/es/components/tag/style/css",
  "element-plus/es/components/pagination/style/css",
  "element-plus/es/components/table/style/css",
  "element-plus/es/components/table-v2/style/css",
  "element-plus/es/components/table-column/style/css",
  "element-plus/es/components/card/style/css",
  "element-plus/es/components/row/style/css",
  "element-plus/es/components/button/style/css",
  "element-plus/es/components/menu/style/css",
  "element-plus/es/components/sub-menu/style/css",
  "element-plus/es/components/menu-item/style/css",
  "element-plus/es/components/option/style/css",
  "element-plus/es/components/dropdown/style/css",
  "element-plus/es/components/dropdown-menu/style/css",
  "element-plus/es/components/dropdown-item/style/css",
  "element-plus/es/components/skeleton/style/css",
  "element-plus/es/components/skeleton/style/css",
  "element-plus/es/components/backtop/style/css",
  "element-plus/es/components/menu/style/css",
  "element-plus/es/components/sub-menu/style/css",
  "element-plus/es/components/menu-item/style/css",
  "element-plus/es/components/dropdown/style/css",
  "element-plus/es/components/tree/style/css",
  "element-plus/es/components/dropdown-menu/style/css",
  "element-plus/es/components/dropdown-item/style/css",
  "element-plus/es/components/badge/style/css",
  "element-plus/es/components/breadcrumb/style/css",
  "element-plus/es/components/breadcrumb-item/style/css",
  "element-plus/es/components/image/style/css",
  "element-plus/es/components/collapse-transition/style/css",
  "element-plus/es/components/timeline/style/css",
  "element-plus/es/components/timeline-item/style/css",
  "element-plus/es/components/collapse/style/css",
  "element-plus/es/components/collapse-item/style/css",
  "element-plus/es/components/button-group/style/css",
  "element-plus/es/components/text/style/css",
  "element-plus/es/components/segmented/style/css",
  "@element-plus/icons-vue",
  "element-plus/es/components/footer/style/css",
  "element-plus/es/components/empty/style/css"
];
var exclude = ["@iconify/json"];

// vite.config.ts
var root = process.cwd();
function pathResolve(dir) {
  return resolve2(root, ".", dir);
}
var vite_config_default = ({ command, mode }) => {
  let env = {};
  const isBuild = command === "build";
  if (!isBuild) {
    env = loadEnv(process.argv[3] === "--mode" ? process.argv[4] : process.argv[3], root);
  } else {
    env = loadEnv(mode, root);
  }
  return {
    base: env.VITE_BASE_PATH,
    root,
    // 服务端渲染
    server: {
      port: env.VITE_PORT,
      // 端口号
      host: "0.0.0.0",
      open: env.VITE_OPEN === "true",
      // 本地跨域代理. 目前注释的原因：暂时没有用途，server 端已经支持跨域
      proxy: {
        ["/admin-api"]: {
          target: env.VITE_BASE_URL,
          ws: false,
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp(`^/admin-api`), "")
        }
      }
      // hmr: {
      //     overlay: false // 禁用开发服务器错误的屏蔽
      // }
    },
    // 项目使用的vite插件。 单独提取到build/vite/plugin中管理
    plugins: createVitePlugins(),
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@/styles/variables.scss" as *;',
          javascriptEnabled: true,
          silenceDeprecations: ["legacy-js-api"]
          // 参考自 https://stackoverflow.com/questions/78997907/the-legacy-js-api-is-deprecated-and-will-be-removed-in-dart-sass-2-0-0
        }
      }
    },
    resolve: {
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".scss", ".css"],
      alias: [
        {
          find: "vue-i18n",
          replacement: "vue-i18n/dist/vue-i18n.cjs.js"
        },
        {
          find: /\@\//,
          replacement: `${pathResolve("src")}/`
        }
      ]
    },
    build: {
      minify: "terser",
      outDir: env.VITE_OUT_DIR || "dist",
      // sourcemap: env.VITE_SOURCEMAP === 'true' ? 'inline' : false,
      sourcemap: true,
      // brotliSize: false,
      terserOptions: {
        compress: {
          drop_debugger: env.VITE_DROP_DEBUGGER === "true",
          drop_console: env.VITE_DROP_CONSOLE === "true"
        }
      },
      rollupOptions: {
        output: {
          manualChunks: {
            echarts: ["echarts"]
            // 将 echarts 单独打包，参考 https://gitee.com/yudaocode/yudao-ui-admin-vue3/issues/IAB1SX 讨论
          }
        }
      }
    },
    optimizeDeps: { include, exclude }
  };
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiLCAiYnVpbGQvdml0ZS9pbmRleC50cyIsICJidWlsZC92aXRlL29wdGltaXplLnRzIl0sCiAgInNvdXJjZXNDb250ZW50IjogWyJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRTpcXFxcWkREXFxcXERlc2t0b3BcXFxcXHU5NUU4XHU3OTgxXFxcXGhkeC1lbnRyYW5jZS1ndWFyZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcWkREXFxcXERlc2t0b3BcXFxcXHU5NUU4XHU3OTgxXFxcXGhkeC1lbnRyYW5jZS1ndWFyZFxcXFx2aXRlLmNvbmZpZy50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRTovWkREL0Rlc2t0b3AvJUU5JTk3JUE4JUU3JUE2JTgxL2hkeC1lbnRyYW5jZS1ndWFyZC92aXRlLmNvbmZpZy50c1wiO2ltcG9ydCB7cmVzb2x2ZX0gZnJvbSAncGF0aCdcclxuaW1wb3J0IHR5cGUge0NvbmZpZ0VudiwgVXNlckNvbmZpZ30gZnJvbSAndml0ZSdcclxuaW1wb3J0IHtsb2FkRW52fSBmcm9tICd2aXRlJ1xyXG5pbXBvcnQge2NyZWF0ZVZpdGVQbHVnaW5zfSBmcm9tICcuL2J1aWxkL3ZpdGUnXHJcbmltcG9ydCB7ZXhjbHVkZSwgaW5jbHVkZX0gZnJvbSBcIi4vYnVpbGQvdml0ZS9vcHRpbWl6ZVwiXHJcbi8vIFx1NUY1M1x1NTI0RFx1NjI2N1x1ODg0Q25vZGVcdTU0N0RcdTRFRTRcdTY1RjZcdTY1ODdcdTRFRjZcdTU5MzlcdTc2ODRcdTU3MzBcdTU3NDAoXHU1REU1XHU0RjVDXHU3NkVFXHU1RjU1KVxyXG5jb25zdCByb290ID0gcHJvY2Vzcy5jd2QoKVxyXG5cclxuLy8gXHU4REVGXHU1Rjg0XHU2N0U1XHU2MjdFXHJcbmZ1bmN0aW9uIHBhdGhSZXNvbHZlKGRpcjogc3RyaW5nKSB7XHJcbiAgICByZXR1cm4gcmVzb2x2ZShyb290LCAnLicsIGRpcilcclxufVxyXG5cclxuLy8gaHR0cHM6Ly92aXRlanMuZGV2L2NvbmZpZy9cclxuZXhwb3J0IGRlZmF1bHQgKHtjb21tYW5kLCBtb2RlfTogQ29uZmlnRW52KTogVXNlckNvbmZpZyA9PiB7XHJcbiAgICBsZXQgZW52ID0ge30gYXMgYW55XHJcbiAgICBjb25zdCBpc0J1aWxkID0gY29tbWFuZCA9PT0gJ2J1aWxkJ1xyXG4gICAgaWYgKCFpc0J1aWxkKSB7XHJcbiAgICAgICAgZW52ID0gbG9hZEVudigocHJvY2Vzcy5hcmd2WzNdID09PSAnLS1tb2RlJyA/IHByb2Nlc3MuYXJndls0XSA6IHByb2Nlc3MuYXJndlszXSksIHJvb3QpXHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICAgIGVudiA9IGxvYWRFbnYobW9kZSwgcm9vdClcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICAgIGJhc2U6IGVudi5WSVRFX0JBU0VfUEFUSCxcclxuICAgICAgICByb290OiByb290LFxyXG4gICAgICAgIC8vIFx1NjcwRFx1NTJBMVx1N0FFRlx1NkUzMlx1NjdEM1xyXG4gICAgICAgIHNlcnZlcjoge1xyXG4gICAgICAgICAgICBwb3J0OiBlbnYuVklURV9QT1JULCAvLyBcdTdBRUZcdTUzRTNcdTUzRjdcclxuICAgICAgICAgICAgaG9zdDogXCIwLjAuMC4wXCIsXHJcbiAgICAgICAgICAgIG9wZW46IGVudi5WSVRFX09QRU4gPT09ICd0cnVlJyxcclxuICAgICAgICAgICAgLy8gXHU2NzJDXHU1NzMwXHU4REU4XHU1N0RGXHU0RUUzXHU3NDA2LiBcdTc2RUVcdTUyNERcdTZDRThcdTkxQ0FcdTc2ODRcdTUzOUZcdTU2RTBcdUZGMUFcdTY2ODJcdTY1RjZcdTZDQTFcdTY3MDlcdTc1MjhcdTkwMTRcdUZGMENzZXJ2ZXIgXHU3QUVGXHU1REYyXHU3RUNGXHU2NTJGXHU2MzAxXHU4REU4XHU1N0RGXHJcbiAgICAgICAgICAgIHByb3h5OiB7XHJcbiAgICAgICAgICAgICAgWycvYWRtaW4tYXBpJ106IHtcclxuICAgICAgICAgICAgICAgIHRhcmdldDogZW52LlZJVEVfQkFTRV9VUkwsXHJcbiAgICAgICAgICAgICAgICB3czogZmFsc2UsXHJcbiAgICAgICAgICAgICAgICBjaGFuZ2VPcmlnaW46IHRydWUsXHJcbiAgICAgICAgICAgICAgICByZXdyaXRlOiAocGF0aCkgPT4gcGF0aC5yZXBsYWNlKG5ldyBSZWdFeHAoYF4vYWRtaW4tYXBpYCksICcnKSxcclxuICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAvLyBobXI6IHtcclxuICAgICAgICAgICAgLy8gICAgIG92ZXJsYXk6IGZhbHNlIC8vIFx1Nzk4MVx1NzUyOFx1NUYwMFx1NTNEMVx1NjcwRFx1NTJBMVx1NTY2OFx1OTUxOVx1OEJFRlx1NzY4NFx1NUM0Rlx1ODUzRFxyXG4gICAgICAgICAgICAvLyB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICAvLyBcdTk4NzlcdTc2RUVcdTRGN0ZcdTc1MjhcdTc2ODR2aXRlXHU2M0QyXHU0RUY2XHUzMDAyIFx1NTM1NVx1NzJFQ1x1NjNEMFx1NTNENlx1NTIzMGJ1aWxkL3ZpdGUvcGx1Z2luXHU0RTJEXHU3QkExXHU3NDA2XHJcbiAgICAgICAgcGx1Z2luczogY3JlYXRlVml0ZVBsdWdpbnMoKSxcclxuICAgICAgICBjc3M6IHtcclxuICAgICAgICAgICAgcHJlcHJvY2Vzc29yT3B0aW9uczoge1xyXG4gICAgICAgICAgICAgICAgc2Nzczoge1xyXG4gICAgICAgICAgICAgICAgICAgIGFkZGl0aW9uYWxEYXRhOiAnQHVzZSBcIkAvc3R5bGVzL3ZhcmlhYmxlcy5zY3NzXCIgYXMgKjsnLFxyXG4gICAgICAgICAgICAgICAgICAgIGphdmFzY3JpcHRFbmFibGVkOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgIHNpbGVuY2VEZXByZWNhdGlvbnM6IFtcImxlZ2FjeS1qcy1hcGlcIl0sIC8vIFx1NTNDMlx1ODAwM1x1ODFFQSBodHRwczovL3N0YWNrb3ZlcmZsb3cuY29tL3F1ZXN0aW9ucy83ODk5NzkwNy90aGUtbGVnYWN5LWpzLWFwaS1pcy1kZXByZWNhdGVkLWFuZC13aWxsLWJlLXJlbW92ZWQtaW4tZGFydC1zYXNzLTItMC0wXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIHJlc29sdmU6IHtcclxuICAgICAgICAgICAgZXh0ZW5zaW9uczogWycubWpzJywgJy5qcycsICcudHMnLCAnLmpzeCcsICcudHN4JywgJy5qc29uJywgJy5zY3NzJywgJy5jc3MnXSxcclxuICAgICAgICAgICAgYWxpYXM6IFtcclxuICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICBmaW5kOiAndnVlLWkxOG4nLFxyXG4gICAgICAgICAgICAgICAgICAgIHJlcGxhY2VtZW50OiAndnVlLWkxOG4vZGlzdC92dWUtaTE4bi5janMuanMnXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgIGZpbmQ6IC9cXEBcXC8vLFxyXG4gICAgICAgICAgICAgICAgICAgIHJlcGxhY2VtZW50OiBgJHtwYXRoUmVzb2x2ZSgnc3JjJyl9L2BcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgXVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgYnVpbGQ6IHtcclxuICAgICAgICAgICAgbWluaWZ5OiAndGVyc2VyJyxcclxuICAgICAgICAgICAgb3V0RGlyOiBlbnYuVklURV9PVVRfRElSIHx8ICdkaXN0JyxcclxuICAgICAgICAgICAgLy8gc291cmNlbWFwOiBlbnYuVklURV9TT1VSQ0VNQVAgPT09ICd0cnVlJyA/ICdpbmxpbmUnIDogZmFsc2UsXHJcbiAgICAgICAgICAgIHNvdXJjZW1hcDogdHJ1ZSxcclxuICAgICAgICAgICAgLy8gYnJvdGxpU2l6ZTogZmFsc2UsXHJcbiAgICAgICAgICAgIHRlcnNlck9wdGlvbnM6IHtcclxuICAgICAgICAgICAgICAgIGNvbXByZXNzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgZHJvcF9kZWJ1Z2dlcjogZW52LlZJVEVfRFJPUF9ERUJVR0dFUiA9PT0gJ3RydWUnLFxyXG4gICAgICAgICAgICAgICAgICAgIGRyb3BfY29uc29sZTogZW52LlZJVEVfRFJPUF9DT05TT0xFID09PSAndHJ1ZSdcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgcm9sbHVwT3B0aW9uczoge1xyXG4gICAgICAgICAgICAgICAgb3V0cHV0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFudWFsQ2h1bmtzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGVjaGFydHM6IFsnZWNoYXJ0cyddIC8vIFx1NUMwNiBlY2hhcnRzIFx1NTM1NVx1NzJFQ1x1NjI1M1x1NTMwNVx1RkYwQ1x1NTNDMlx1ODAwMyBodHRwczovL2dpdGVlLmNvbS95dWRhb2NvZGUveXVkYW8tdWktYWRtaW4tdnVlMy9pc3N1ZXMvSUFCMVNYIFx1OEJBOFx1OEJCQVxyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICBvcHRpbWl6ZURlcHM6IHtpbmNsdWRlLCBleGNsdWRlfVxyXG4gICAgfVxyXG59XHJcbiIsICJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRTpcXFxcWkREXFxcXERlc2t0b3BcXFxcXHU5NUU4XHU3OTgxXFxcXGhkeC1lbnRyYW5jZS1ndWFyZFxcXFxidWlsZFxcXFx2aXRlXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJFOlxcXFxaRERcXFxcRGVza3RvcFxcXFxcdTk1RThcdTc5ODFcXFxcaGR4LWVudHJhbmNlLWd1YXJkXFxcXGJ1aWxkXFxcXHZpdGVcXFxcaW5kZXgudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0U6L1pERC9EZXNrdG9wLyVFOSU5NyVBOCVFNyVBNiU4MS9oZHgtZW50cmFuY2UtZ3VhcmQvYnVpbGQvdml0ZS9pbmRleC50c1wiO2ltcG9ydCB7IHJlc29sdmUgfSBmcm9tICdwYXRoJ1xyXG5pbXBvcnQgVnVlIGZyb20gJ0B2aXRlanMvcGx1Z2luLXZ1ZSdcclxuaW1wb3J0IFZ1ZUpzeCBmcm9tICdAdml0ZWpzL3BsdWdpbi12dWUtanN4J1xyXG5pbXBvcnQgcHJvZ3Jlc3MgZnJvbSAndml0ZS1wbHVnaW4tcHJvZ3Jlc3MnXHJcbmltcG9ydCBFc2xpbnRQbHVnaW4gZnJvbSAndml0ZS1wbHVnaW4tZXNsaW50J1xyXG5pbXBvcnQgUHVyZ2VJY29ucyBmcm9tICd2aXRlLXBsdWdpbi1wdXJnZS1pY29ucydcclxuaW1wb3J0IHsgVml0ZUVqc1BsdWdpbiB9IGZyb20gJ3ZpdGUtcGx1Z2luLWVqcydcclxuLy8gQHRzLWlnbm9yZVxyXG5pbXBvcnQgRWxlbWVudFBsdXMgZnJvbSAndW5wbHVnaW4tZWxlbWVudC1wbHVzL3ZpdGUnXHJcbmltcG9ydCBBdXRvSW1wb3J0IGZyb20gJ3VucGx1Z2luLWF1dG8taW1wb3J0L3ZpdGUnXHJcbmltcG9ydCBDb21wb25lbnRzIGZyb20gJ3VucGx1Z2luLXZ1ZS1jb21wb25lbnRzL3ZpdGUnXHJcbmltcG9ydCB7IEVsZW1lbnRQbHVzUmVzb2x2ZXIgfSBmcm9tICd1bnBsdWdpbi12dWUtY29tcG9uZW50cy9yZXNvbHZlcnMnXHJcbmltcG9ydCB2aXRlQ29tcHJlc3Npb24gZnJvbSAndml0ZS1wbHVnaW4tY29tcHJlc3Npb24nXHJcbmltcG9ydCB0b3BMZXZlbEF3YWl0IGZyb20gJ3ZpdGUtcGx1Z2luLXRvcC1sZXZlbC1hd2FpdCdcclxuaW1wb3J0IFZ1ZUkxOG5QbHVnaW4gZnJvbSAnQGludGxpZnkvdW5wbHVnaW4tdnVlLWkxOG4vdml0ZSdcclxuaW1wb3J0IHsgY3JlYXRlU3ZnSWNvbnNQbHVnaW4gfSBmcm9tICd2aXRlLXBsdWdpbi1zdmctaWNvbnMnXHJcbmltcG9ydCBVbm9DU1MgZnJvbSAndW5vY3NzL3ZpdGUnXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlVml0ZVBsdWdpbnMoKSB7XHJcbiAgY29uc3Qgcm9vdCA9IHByb2Nlc3MuY3dkKClcclxuXHJcbiAgLy8gXHU4REVGXHU1Rjg0XHU2N0U1XHU2MjdFXHJcbiAgZnVuY3Rpb24gcGF0aFJlc29sdmUoZGlyOiBzdHJpbmcpIHtcclxuICAgIHJldHVybiByZXNvbHZlKHJvb3QsICcuJywgZGlyKVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIFtcclxuICAgIFZ1ZSgpLFxyXG4gICAgVnVlSnN4KCksXHJcbiAgICBVbm9DU1MoKSxcclxuICAgIHByb2dyZXNzKCksXHJcbiAgICAvLyBQdXJnZUljb25zKCksXHJcbiAgICBFbGVtZW50UGx1cyh7fSksXHJcbiAgICBBdXRvSW1wb3J0KHtcclxuICAgICAgaW5jbHVkZTogW1xyXG4gICAgICAgIC9cXC5bdGpdc3g/JC8sIC8vIC50cywgLnRzeCwgLmpzLCAuanN4XHJcbiAgICAgICAgL1xcLnZ1ZSQvLFxyXG4gICAgICAgIC9cXC52dWVcXD92dWUvLCAvLyAudnVlXHJcbiAgICAgICAgL1xcLm1kJC8gLy8gLm1kXHJcbiAgICAgIF0sXHJcbiAgICAgIGltcG9ydHM6IFtcclxuICAgICAgICAndnVlJyxcclxuICAgICAgICAndnVlLXJvdXRlcicsXHJcbiAgICAgICAgLy8gXHU1M0VGXHU5ODlEXHU1OTE2XHU2REZCXHU1MkEwXHU5NzAwXHU4OTgxIGF1dG9JbXBvcnQgXHU3Njg0XHU3RUM0XHU0RUY2XHJcbiAgICAgICAge1xyXG4gICAgICAgICAgJ0AvaG9va3Mvd2ViL3VzZUkxOG4nOiBbJ3VzZUkxOG4nXSxcclxuICAgICAgICAgICdAL2hvb2tzL3dlYi91c2VNZXNzYWdlJzogWyd1c2VNZXNzYWdlJ10sXHJcbiAgICAgICAgICAnQC9ob29rcy93ZWIvdXNlVGFibGUnOiBbJ3VzZVRhYmxlJ10sXHJcbiAgICAgICAgICAnQC9ob29rcy93ZWIvdXNlQ3J1ZFNjaGVtYXMnOiBbJ3VzZUNydWRTY2hlbWFzJ10sXHJcbiAgICAgICAgICAnQC91dGlscy9mb3JtUnVsZXMnOiBbJ3JlcXVpcmVkJ10sXHJcbiAgICAgICAgICAnQC91dGlscy9kaWN0JzogWydESUNUX1RZUEUnXVxyXG4gICAgICAgIH1cclxuICAgICAgXSxcclxuICAgICAgZHRzOiAnc3JjL3R5cGVzL2F1dG8taW1wb3J0cy5kLnRzJyxcclxuICAgICAgcmVzb2x2ZXJzOiBbRWxlbWVudFBsdXNSZXNvbHZlcigpXSxcclxuICAgICAgZXNsaW50cmM6IHtcclxuICAgICAgICBlbmFibGVkOiBmYWxzZSwgLy8gRGVmYXVsdCBgZmFsc2VgXHJcbiAgICAgICAgZmlsZXBhdGg6ICcuLy5lc2xpbnRyYy1hdXRvLWltcG9ydC5qc29uJywgLy8gRGVmYXVsdCBgLi8uZXNsaW50cmMtYXV0by1pbXBvcnQuanNvbmBcclxuICAgICAgICBnbG9iYWxzUHJvcFZhbHVlOiB0cnVlIC8vIERlZmF1bHQgYHRydWVgLCAodHJ1ZSB8IGZhbHNlIHwgJ3JlYWRvbmx5JyB8ICdyZWFkYWJsZScgfCAnd3JpdGFibGUnIHwgJ3dyaXRlYWJsZScpXHJcbiAgICAgIH1cclxuICAgIH0pLFxyXG4gICAgQ29tcG9uZW50cyh7XHJcbiAgICAgIC8vIFx1NzUxRlx1NjIxMFx1ODFFQVx1NUI5QVx1NEU0OSBgYXV0by1jb21wb25lbnRzLmQudHNgIFx1NTE2OFx1NUM0MFx1NThGMFx1NjYwRVxyXG4gICAgICBkdHM6ICdzcmMvdHlwZXMvYXV0by1jb21wb25lbnRzLmQudHMnLFxyXG4gICAgICAvLyBcdTgxRUFcdTVCOUFcdTRFNDlcdTdFQzRcdTRFRjZcdTc2ODRcdTg5RTNcdTY3OTBcdTU2NjhcclxuICAgICAgcmVzb2x2ZXJzOiBbRWxlbWVudFBsdXNSZXNvbHZlcigpXSxcclxuICAgICAgZ2xvYnM6IFtcInNyYy9jb21wb25lbnRzLyoqLyoqLnt2dWUsIG1kfVwiLCAnIXNyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS8qKiddXHJcbiAgICB9KSxcclxuICAgIEVzbGludFBsdWdpbih7XHJcbiAgICAgIGNhY2hlOiBmYWxzZSxcclxuICAgICAgaW5jbHVkZTogWydzcmMvKiovKi52dWUnLCAnc3JjLyoqLyoudHMnLCAnc3JjLyoqLyoudHN4J10gLy8gXHU2OEMwXHU2N0U1XHU3Njg0XHU2NTg3XHU0RUY2XHJcbiAgICB9KSxcclxuICAgIFZ1ZUkxOG5QbHVnaW4oe1xyXG4gICAgICBydW50aW1lT25seTogdHJ1ZSxcclxuICAgICAgY29tcG9zaXRpb25Pbmx5OiB0cnVlLFxyXG4gICAgICBpbmNsdWRlOiBbcmVzb2x2ZShfX2Rpcm5hbWUsICdzcmMvbG9jYWxlcy8qKicpXVxyXG4gICAgfSksXHJcbiAgICBjcmVhdGVTdmdJY29uc1BsdWdpbih7XHJcbiAgICAgIGljb25EaXJzOiBbcGF0aFJlc29sdmUoJ3NyYy9hc3NldHMvc3ZncycpXSxcclxuICAgICAgc3ltYm9sSWQ6ICdpY29uLVtkaXJdLVtuYW1lXScsXHJcbiAgICAgIHN2Z29PcHRpb25zOiB0cnVlXHJcbiAgICB9KSxcclxuICAgIHZpdGVDb21wcmVzc2lvbih7XHJcbiAgICAgIHZlcmJvc2U6IHRydWUsIC8vIFx1NjYyRlx1NTQyNlx1NTcyOFx1NjNBN1x1NTIzNlx1NTNGMFx1OEY5M1x1NTFGQVx1NTM4Qlx1N0YyOVx1N0VEM1x1Njc5Q1xyXG4gICAgICBkaXNhYmxlOiBmYWxzZSwgLy8gXHU2NjJGXHU1NDI2XHU3OTgxXHU3NTI4XHJcbiAgICAgIHRocmVzaG9sZDogMTAyNDAsIC8vIFx1NEY1M1x1NzlFRlx1NTkyN1x1NEU4RSB0aHJlc2hvbGQgXHU2MjREXHU0RjFBXHU4OEFCXHU1MzhCXHU3RjI5LFx1NTM1NVx1NEY0RCBiXHJcbiAgICAgIGFsZ29yaXRobTogJ2d6aXAnLCAvLyBcdTUzOEJcdTdGMjlcdTdCOTdcdTZDRDUsXHU1M0VGXHU5MDA5IFsgJ2d6aXAnICwgJ2Jyb3RsaUNvbXByZXNzJyAsJ2RlZmxhdGUnICwgJ2RlZmxhdGVSYXcnXVxyXG4gICAgICBleHQ6ICcuZ3onLCAvLyBcdTc1MUZcdTYyMTBcdTc2ODRcdTUzOEJcdTdGMjlcdTUzMDVcdTU0MEVcdTdGMDBcclxuICAgICAgZGVsZXRlT3JpZ2luRmlsZTogZmFsc2UgLy9cdTUzOEJcdTdGMjlcdTU0MEVcdTY2MkZcdTU0MjZcdTUyMjBcdTk2NjRcdTZFOTBcdTY1ODdcdTRFRjZcclxuICAgIH0pLFxyXG4gICAgVml0ZUVqc1BsdWdpbigpLFxyXG4gICAgdG9wTGV2ZWxBd2FpdCh7XHJcbiAgICAgIC8vIGh0dHBzOi8vanVlamluLmNuL3Bvc3QvNzE1MjE5MTc0MjUxMzUxMjQ4NVxyXG4gICAgICAvLyBUaGUgZXhwb3J0IG5hbWUgb2YgdG9wLWxldmVsIGF3YWl0IHByb21pc2UgZm9yIGVhY2ggY2h1bmsgbW9kdWxlXHJcbiAgICAgIHByb21pc2VFeHBvcnROYW1lOiAnX190bGEnLFxyXG4gICAgICAvLyBUaGUgZnVuY3Rpb24gdG8gZ2VuZXJhdGUgaW1wb3J0IG5hbWVzIG9mIHRvcC1sZXZlbCBhd2FpdCBwcm9taXNlIGluIGVhY2ggY2h1bmsgbW9kdWxlXHJcbiAgICAgIHByb21pc2VJbXBvcnROYW1lOiAoaSkgPT4gYF9fdGxhXyR7aX1gXHJcbiAgICB9KVxyXG4gIF1cclxufVxyXG4iLCAiY29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2Rpcm5hbWUgPSBcIkU6XFxcXFpERFxcXFxEZXNrdG9wXFxcXFx1OTVFOFx1Nzk4MVxcXFxoZHgtZW50cmFuY2UtZ3VhcmRcXFxcYnVpbGRcXFxcdml0ZVwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRTpcXFxcWkREXFxcXERlc2t0b3BcXFxcXHU5NUU4XHU3OTgxXFxcXGhkeC1lbnRyYW5jZS1ndWFyZFxcXFxidWlsZFxcXFx2aXRlXFxcXG9wdGltaXplLnRzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9FOi9aREQvRGVza3RvcC8lRTklOTclQTglRTclQTYlODEvaGR4LWVudHJhbmNlLWd1YXJkL2J1aWxkL3ZpdGUvb3B0aW1pemUudHNcIjtjb25zdCBpbmNsdWRlID0gW1xyXG4gICdxcycsXHJcbiAgJ3VybCcsXHJcbiAgJ3Z1ZScsXHJcbiAgJ3Nhc3MnLFxyXG4gICdtaXR0JyxcclxuICAnYXhpb3MnLFxyXG4gICdwaW5pYScsXHJcbiAgJ2RheWpzJyxcclxuICAncXJjb2RlJyxcclxuICAndW5vY3NzJyxcclxuICAndnVlLXJvdXRlcicsXHJcbiAgJ3Z1ZS10eXBlcycsXHJcbiAgJ3Z1ZS1pMThuJyxcclxuICAnY3J5cHRvLWpzJyxcclxuICAnY3JvcHBlcmpzJyxcclxuICAnbG9kYXNoLWVzJyxcclxuICAnbnByb2dyZXNzJyxcclxuICAnd2ViLXN0b3JhZ2UtY2FjaGUnLFxyXG4gICdAaWNvbmlmeS9pY29uaWZ5JyxcclxuICAnQHZ1ZXVzZS9jb3JlJyxcclxuICAnQHp4Y3Zibi10cy9jb3JlJyxcclxuICAnZWNoYXJ0cy9jb3JlJyxcclxuICAnZWNoYXJ0cy9jaGFydHMnLFxyXG4gICdlY2hhcnRzL2NvbXBvbmVudHMnLFxyXG4gICdlY2hhcnRzL3JlbmRlcmVycycsXHJcbiAgJ2VjaGFydHMtd29yZGNsb3VkJyxcclxuICAnQHdhbmdlZGl0b3IvZWRpdG9yJyxcclxuICAnQHdhbmdlZGl0b3IvZWRpdG9yLWZvci12dWUnLFxyXG4gICdAbWljcm9zb2Z0L2ZldGNoLWV2ZW50LXNvdXJjZScsXHJcbiAgJ21hcmtkb3duLWl0JyxcclxuICAnbWFya21hcC12aWV3JyxcclxuICAnbWFya21hcC1saWInLFxyXG4gICdtYXJrbWFwLXRvb2xiYXInLFxyXG4gICdoaWdobGlnaHQuanMnLFxyXG4gICdlbGVtZW50LXBsdXMnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvbG9jYWxlL2xhbmcvemgtY24nLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvbG9jYWxlL2xhbmcvZW4nLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9hdmF0YXIvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvc3BhY2Uvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvYmFja3RvcC9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9mb3JtL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3JhZGlvLWdyb3VwL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3JhZGlvL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NoZWNrYm94L3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NoZWNrYm94LWdyb3VwL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3N3aXRjaC9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90aW1lLXBpY2tlci9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kYXRlLXBpY2tlci9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kZXNjcmlwdGlvbnMvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZGVzY3JpcHRpb25zLWl0ZW0vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvbGluay9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90b29sdGlwL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2RyYXdlci9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kaWFsb2cvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvY2hlY2tib3gtYnV0dG9uL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL29wdGlvbi1ncm91cC9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9yYWRpby1idXR0b24vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvY2FzY2FkZXIvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvY29sb3ItcGlja2VyL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2lucHV0LW51bWJlci9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9yYXRlL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3NlbGVjdC12Mi9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90cmVlLXNlbGVjdC9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9zbGlkZXIvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdGltZS1zZWxlY3Qvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvYXV0b2NvbXBsZXRlL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2ltYWdlLXZpZXdlci9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy91cGxvYWQvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvY29sL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2Zvcm0taXRlbS9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9hbGVydC9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9icmVhZGNydW1iL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3NlbGVjdC9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9pbnB1dC9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9icmVhZGNydW1iLWl0ZW0vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdGFnL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3BhZ2luYXRpb24vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdGFibGUvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdGFibGUtdjIvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdGFibGUtY29sdW1uL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NhcmQvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvcm93L3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2J1dHRvbi9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9tZW51L3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3N1Yi1tZW51L3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL21lbnUtaXRlbS9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9vcHRpb24vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZHJvcGRvd24vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZHJvcGRvd24tbWVudS9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kcm9wZG93bi1pdGVtL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3NrZWxldG9uL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3NrZWxldG9uL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2JhY2t0b3Avc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvbWVudS9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9zdWItbWVudS9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9tZW51LWl0ZW0vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZHJvcGRvd24vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvdHJlZS9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9kcm9wZG93bi1tZW51L3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2Ryb3Bkb3duLWl0ZW0vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvYmFkZ2Uvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvYnJlYWRjcnVtYi9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy9icmVhZGNydW1iLWl0ZW0vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvaW1hZ2Uvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvY29sbGFwc2UtdHJhbnNpdGlvbi9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90aW1lbGluZS9zdHlsZS9jc3MnLFxyXG4gICdlbGVtZW50LXBsdXMvZXMvY29tcG9uZW50cy90aW1lbGluZS1pdGVtL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NvbGxhcHNlL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2NvbGxhcHNlLWl0ZW0vc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvYnV0dG9uLWdyb3VwL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL3RleHQvc3R5bGUvY3NzJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvc2VnbWVudGVkL3N0eWxlL2NzcycsXHJcbiAgJ0BlbGVtZW50LXBsdXMvaWNvbnMtdnVlJyxcclxuICAnZWxlbWVudC1wbHVzL2VzL2NvbXBvbmVudHMvZm9vdGVyL3N0eWxlL2NzcycsXHJcbiAgJ2VsZW1lbnQtcGx1cy9lcy9jb21wb25lbnRzL2VtcHR5L3N0eWxlL2NzcydcclxuXVxyXG5cclxuY29uc3QgZXhjbHVkZSA9IFsnQGljb25pZnkvanNvbiddXHJcblxyXG5leHBvcnQgeyBpbmNsdWRlLCBleGNsdWRlIH1cclxuIl0sCiAgIm1hcHBpbmdzIjogIjtBQUF3VCxTQUFRLFdBQUFBLGdCQUFjO0FBRTlVLFNBQVEsZUFBYzs7O0FDRjJULFNBQVMsZUFBZTtBQUN6VyxPQUFPLFNBQVM7QUFDaEIsT0FBTyxZQUFZO0FBQ25CLE9BQU8sY0FBYztBQUNyQixPQUFPLGtCQUFrQjtBQUV6QixTQUFTLHFCQUFxQjtBQUU5QixPQUFPLGlCQUFpQjtBQUN4QixPQUFPLGdCQUFnQjtBQUN2QixPQUFPLGdCQUFnQjtBQUN2QixTQUFTLDJCQUEyQjtBQUNwQyxPQUFPLHFCQUFxQjtBQUM1QixPQUFPLG1CQUFtQjtBQUMxQixPQUFPLG1CQUFtQjtBQUMxQixTQUFTLDRCQUE0QjtBQUNyQyxPQUFPLFlBQVk7QUFoQm5CLElBQU0sbUNBQW1DO0FBa0JsQyxTQUFTLG9CQUFvQjtBQUNsQyxRQUFNQyxRQUFPLFFBQVEsSUFBSTtBQUd6QixXQUFTQyxhQUFZLEtBQWE7QUFDaEMsV0FBTyxRQUFRRCxPQUFNLEtBQUssR0FBRztBQUFBLEVBQy9CO0FBRUEsU0FBTztBQUFBLElBQ0wsSUFBSTtBQUFBLElBQ0osT0FBTztBQUFBLElBQ1AsT0FBTztBQUFBLElBQ1AsU0FBUztBQUFBO0FBQUEsSUFFVCxZQUFZLENBQUMsQ0FBQztBQUFBLElBQ2QsV0FBVztBQUFBLE1BQ1QsU0FBUztBQUFBLFFBQ1A7QUFBQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUE7QUFBQSxRQUNBO0FBQUE7QUFBQSxNQUNGO0FBQUEsTUFDQSxTQUFTO0FBQUEsUUFDUDtBQUFBLFFBQ0E7QUFBQTtBQUFBLFFBRUE7QUFBQSxVQUNFLHVCQUF1QixDQUFDLFNBQVM7QUFBQSxVQUNqQywwQkFBMEIsQ0FBQyxZQUFZO0FBQUEsVUFDdkMsd0JBQXdCLENBQUMsVUFBVTtBQUFBLFVBQ25DLDhCQUE4QixDQUFDLGdCQUFnQjtBQUFBLFVBQy9DLHFCQUFxQixDQUFDLFVBQVU7QUFBQSxVQUNoQyxnQkFBZ0IsQ0FBQyxXQUFXO0FBQUEsUUFDOUI7QUFBQSxNQUNGO0FBQUEsTUFDQSxLQUFLO0FBQUEsTUFDTCxXQUFXLENBQUMsb0JBQW9CLENBQUM7QUFBQSxNQUNqQyxVQUFVO0FBQUEsUUFDUixTQUFTO0FBQUE7QUFBQSxRQUNULFVBQVU7QUFBQTtBQUFBLFFBQ1Ysa0JBQWtCO0FBQUE7QUFBQSxNQUNwQjtBQUFBLElBQ0YsQ0FBQztBQUFBLElBQ0QsV0FBVztBQUFBO0FBQUEsTUFFVCxLQUFLO0FBQUE7QUFBQSxNQUVMLFdBQVcsQ0FBQyxvQkFBb0IsQ0FBQztBQUFBLE1BQ2pDLE9BQU8sQ0FBQyxrQ0FBa0MsZ0RBQWdEO0FBQUEsSUFDNUYsQ0FBQztBQUFBLElBQ0QsYUFBYTtBQUFBLE1BQ1gsT0FBTztBQUFBLE1BQ1AsU0FBUyxDQUFDLGdCQUFnQixlQUFlLGNBQWM7QUFBQTtBQUFBLElBQ3pELENBQUM7QUFBQSxJQUNELGNBQWM7QUFBQSxNQUNaLGFBQWE7QUFBQSxNQUNiLGlCQUFpQjtBQUFBLE1BQ2pCLFNBQVMsQ0FBQyxRQUFRLGtDQUFXLGdCQUFnQixDQUFDO0FBQUEsSUFDaEQsQ0FBQztBQUFBLElBQ0QscUJBQXFCO0FBQUEsTUFDbkIsVUFBVSxDQUFDQyxhQUFZLGlCQUFpQixDQUFDO0FBQUEsTUFDekMsVUFBVTtBQUFBLE1BQ1YsYUFBYTtBQUFBLElBQ2YsQ0FBQztBQUFBLElBQ0QsZ0JBQWdCO0FBQUEsTUFDZCxTQUFTO0FBQUE7QUFBQSxNQUNULFNBQVM7QUFBQTtBQUFBLE1BQ1QsV0FBVztBQUFBO0FBQUEsTUFDWCxXQUFXO0FBQUE7QUFBQSxNQUNYLEtBQUs7QUFBQTtBQUFBLE1BQ0wsa0JBQWtCO0FBQUE7QUFBQSxJQUNwQixDQUFDO0FBQUEsSUFDRCxjQUFjO0FBQUEsSUFDZCxjQUFjO0FBQUE7QUFBQTtBQUFBLE1BR1osbUJBQW1CO0FBQUE7QUFBQSxNQUVuQixtQkFBbUIsQ0FBQyxNQUFNLFNBQVMsQ0FBQztBQUFBLElBQ3RDLENBQUM7QUFBQSxFQUNIO0FBQ0Y7OztBQ25HdVYsSUFBTSxVQUFVO0FBQUEsRUFDclc7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQUEsRUFDQTtBQUFBLEVBQ0E7QUFBQSxFQUNBO0FBQ0Y7QUFFQSxJQUFNLFVBQVUsQ0FBQyxlQUFlOzs7QUZqSGhDLElBQU0sT0FBTyxRQUFRLElBQUk7QUFHekIsU0FBUyxZQUFZLEtBQWE7QUFDOUIsU0FBT0MsU0FBUSxNQUFNLEtBQUssR0FBRztBQUNqQztBQUdBLElBQU8sc0JBQVEsQ0FBQyxFQUFDLFNBQVMsS0FBSSxNQUE2QjtBQUN2RCxNQUFJLE1BQU0sQ0FBQztBQUNYLFFBQU0sVUFBVSxZQUFZO0FBQzVCLE1BQUksQ0FBQyxTQUFTO0FBQ1YsVUFBTSxRQUFTLFFBQVEsS0FBSyxDQUFDLE1BQU0sV0FBVyxRQUFRLEtBQUssQ0FBQyxJQUFJLFFBQVEsS0FBSyxDQUFDLEdBQUksSUFBSTtBQUFBLEVBQzFGLE9BQU87QUFDSCxVQUFNLFFBQVEsTUFBTSxJQUFJO0FBQUEsRUFDNUI7QUFFQSxTQUFPO0FBQUEsSUFDSCxNQUFNLElBQUk7QUFBQSxJQUNWO0FBQUE7QUFBQSxJQUVBLFFBQVE7QUFBQSxNQUNKLE1BQU0sSUFBSTtBQUFBO0FBQUEsTUFDVixNQUFNO0FBQUEsTUFDTixNQUFNLElBQUksY0FBYztBQUFBO0FBQUEsTUFFeEIsT0FBTztBQUFBLFFBQ0wsQ0FBQyxZQUFZLEdBQUc7QUFBQSxVQUNkLFFBQVEsSUFBSTtBQUFBLFVBQ1osSUFBSTtBQUFBLFVBQ0osY0FBYztBQUFBLFVBQ2QsU0FBUyxDQUFDLFNBQVMsS0FBSyxRQUFRLElBQUksT0FBTyxhQUFhLEdBQUcsRUFBRTtBQUFBLFFBQy9EO0FBQUEsTUFDRjtBQUFBO0FBQUE7QUFBQTtBQUFBLElBSUo7QUFBQTtBQUFBLElBRUEsU0FBUyxrQkFBa0I7QUFBQSxJQUMzQixLQUFLO0FBQUEsTUFDRCxxQkFBcUI7QUFBQSxRQUNqQixNQUFNO0FBQUEsVUFDRixnQkFBZ0I7QUFBQSxVQUNoQixtQkFBbUI7QUFBQSxVQUNuQixxQkFBcUIsQ0FBQyxlQUFlO0FBQUE7QUFBQSxRQUN6QztBQUFBLE1BQ0o7QUFBQSxJQUNKO0FBQUEsSUFDQSxTQUFTO0FBQUEsTUFDTCxZQUFZLENBQUMsUUFBUSxPQUFPLE9BQU8sUUFBUSxRQUFRLFNBQVMsU0FBUyxNQUFNO0FBQUEsTUFDM0UsT0FBTztBQUFBLFFBQ0g7QUFBQSxVQUNJLE1BQU07QUFBQSxVQUNOLGFBQWE7QUFBQSxRQUNqQjtBQUFBLFFBQ0E7QUFBQSxVQUNJLE1BQU07QUFBQSxVQUNOLGFBQWEsR0FBRyxZQUFZLEtBQUssQ0FBQztBQUFBLFFBQ3RDO0FBQUEsTUFDSjtBQUFBLElBQ0o7QUFBQSxJQUNBLE9BQU87QUFBQSxNQUNILFFBQVE7QUFBQSxNQUNSLFFBQVEsSUFBSSxnQkFBZ0I7QUFBQTtBQUFBLE1BRTVCLFdBQVc7QUFBQTtBQUFBLE1BRVgsZUFBZTtBQUFBLFFBQ1gsVUFBVTtBQUFBLFVBQ04sZUFBZSxJQUFJLHVCQUF1QjtBQUFBLFVBQzFDLGNBQWMsSUFBSSxzQkFBc0I7QUFBQSxRQUM1QztBQUFBLE1BQ0o7QUFBQSxNQUNBLGVBQWU7QUFBQSxRQUNYLFFBQVE7QUFBQSxVQUNKLGNBQWM7QUFBQSxZQUNWLFNBQVMsQ0FBQyxTQUFTO0FBQUE7QUFBQSxVQUN2QjtBQUFBLFFBQ0o7QUFBQSxNQUNKO0FBQUEsSUFDSjtBQUFBLElBQ0EsY0FBYyxFQUFDLFNBQVMsUUFBTztBQUFBLEVBQ25DO0FBQ0o7IiwKICAibmFtZXMiOiBbInJlc29sdmUiLCAicm9vdCIsICJwYXRoUmVzb2x2ZSIsICJyZXNvbHZlIl0KfQo=

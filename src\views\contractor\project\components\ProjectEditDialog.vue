<template>
  <div class="project-edit-dialog">
    <Dialog :title="title" v-model="dialogVisible">
      <BasicForm ref="formRef" :form-schema="formSchema" />
      <template #footer>
        <el-button type="primary" :loading="loading" @click="handleSubmit">确定</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { BasicForm } from '@/components/BasicForm'
import type { IBasicFormExpose, IFormSchema } from '@/components/BasicForm/types'
import { getSimpleDeptList } from '@/api/system/dept'
import { getSimpleUserList } from '@/api/system/user'
import { addProjectAPI, editProjectAPI } from '@/api/contractor/project'
import type { IAddProjectData } from '@/api/contractor/project/types'
import type { UserVO } from '@/api/system/user'

defineOptions({
  name: 'ProjectEditDialog'
})

const emit = defineEmits<{
  (e: 'submit:success'): void
}>()

const message = useMessage()

/* 标题 */
const title = ref('新增项目')

/* 按钮加载状态 */
const loading = ref(false)

/* 是否为编辑状态 */
const isUpdate = ref(false)

/* 对话框可见性 */
const dialogVisible = ref(false)
/* 表单组件实例 */
const formRef = ref<IBasicFormExpose>()

/* 门禁人员 */
const projectUser = ref<UserVO[]>()
/* 承包商人员 */
const contractorUser = ref<UserVO[]>()

/* 表单配置 */
const formSchema: IFormSchema = reactive({
  labelWidth: 110,
  formItems: [
    {
      field: 'projectName',
      label: '项目名称',
      component: 'Input',
      placeholder: '请输入项目名称',
      colProps: {
        span: 24
      },
      rules: {
        required: true,
        message: '请输入项目名称',
        trigger: 'blur'
      }
    },
    {
      field: 'projectId',
      label: '项目来源',
      component: 'Select',
      placeholder: '请选择部门',
      componentProps: {
        optionsLabelField: 'name'
      },
      options: getSimpleDeptList.bind(null, { systemType: 0 }),
      rules: {
        required: true,
        message: '请选择部门',
        trigger: 'change'
      },
      onChange(value) {
        getProjectUserOptions(value)
      }
    },
    {
      field: 'projectUserId',
      label: '项目联系人',
      component: 'Select',
      placeholder: '请选择联系人',
      rules: {
        required: true,
        message: '请选择联系人',
        trigger: 'change'
      },
      options: []
    },
    {
      field: 'contractorId',
      label: '承包商',
      placeholder: '请选择承包商',
      component: 'Select',
      componentProps: {
        optionsLabelField: 'name'
      },
      options: getSimpleDeptList.bind(null, { systemType: 1 }),
      onChange(value) {
        getContractorUserOptions(value)
      },
      rules: {
        required: true,
        message: '请选择承包商',
        trigger: 'change'
      }
    },
    {
      field: 'contractorUserId',
      label: '	承包商联系人',
      component: 'Select',
      options: [],
      placeholder: '请选择承包商联系人',

      rules: {
        required: true,
        message: '请选择承包商联系人',
        trigger: 'change'
      }
    },
    {
      field: 'startTime',
      label: '开始时间',
      component: 'DataTimePicker',
      componentProps: {
        type: 'datetime',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      placeholder: '请选择开始时间',
      rules: {
        required: true,
        message: '请选择开始时间',
        trigger: 'change'
      }
    },
    {
      field: 'endTime',
      label: '结束时间',
      component: 'DataTimePicker',
      placeholder: '请选择结束时间',
      componentProps: {
        type: 'datetime',
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      rules: {
        required: true,
        message: '请选择结束时间',
        trigger: 'change'
      }
    }
  ]
})

/* 打开对话框 */
interface IOpenDialog {
  isUpdate: boolean
  row?: any
}

const open = (params: IOpenDialog = { isUpdate: false }) => {
  console.log(params)
  isUpdate.value = params.isUpdate
  dialogVisible.value = true

  if (!isUpdate.value) return

  const {
    id,
    projectName,
    projectId,
    projectUserId,
    startTime,
    endTime,
    contractorUserId,
    contractorId,
    projectIsFinish,
    projectFinishPercentage
  } = params.row

  getProjectUserOptions(projectId)
  getContractorUserOptions(contractorId)

  nextTick(() => {
    formRef.value?.setFormFieldsValue({
      id,
      projectName,
      projectId,
      projectUserId,
      startTime,
      endTime,
      contractorUserId,
      contractorId,
      projectIsFinish,
      projectFinishPercentage
    })
  })
}

/* 获取用户列表 */
const getProjectUserList = () => {
  getSimpleUserList()
    .then((res) => {
      projectUser.value = res.filter((user) => user.systemType === 0)
      console.log('🚀 ~ .then ~ projectUser.value ====> ', projectUser.value)
      contractorUser.value = res.filter((user) => user.systemType === 1)
      console.log('🚀 ~ .then ~ contractorUser.value ====> ', contractorUser.value)
    })
    .catch((err) => {
      console.log(err)
    })
}

/* 获取承包商人员选项 */
const getContractorUserOptions = (deptId: number) => {
  formSchema.formItems![4].options = contractorUser.value
    ?.filter((user) => user.deptId === deptId)
    .map((item) => ({
      label: item.username,
      value: item.id
    }))

  console.log(formSchema.formItems![4].options)
}

/* 获取项目人员选项 */
const getProjectUserOptions = (deptId: number) => {
  formSchema.formItems![2].options = projectUser.value
    ?.filter((user) => user.deptId === deptId)
    .map((item) => ({
      label: item.username,
      value: item.id
    }))

  console.log(formSchema.formItems![2].options)
}

/* 提交表单数据 */
const handleSubmit = async () => {
  console.log('提交表单数据')
  if (!formRef.value) return
  try {
    const { isValid, formData } = await formRef.value?.customValidate()
    if (!isValid) return
    const apiFn = isUpdate.value ? editProjectAPI : addProjectAPI
    loading.value = true
    console.log(formData)
    await apiFn(formData as IAddProjectData)
    message.success('操作成功')
    emit('submit:success')
    dialogVisible.value = false
  } catch (error) {
    console.log(error)
    message.error('操作失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getProjectUserList()
})

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>

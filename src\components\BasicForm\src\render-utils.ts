import { ElCascader, ElDatePicker, ElInput, ElInputNumber, ElTreeSelect } from 'element-plus'

import Upload from './components/Upload'
import Button from './components/Button'
import Select from './components/Select'
import Radio from './components/Radio'

/**
 * 组件映射表
 */
export const COMPONENT_MAP = {
  Input: ElInput,
  InputNumber: ElInputNumber,
  Select,
  TreeSelect: ElTreeSelect,
  Cascader: ElCascader,
  Button,
  Radio: Radio,
  DataTimePicker: ElDatePicker,
  ManualUpload: Upload
}

/**
 * 不同组件 placeholder 提示映射表
 */
const PLACEHOLDER_MAP = {
  Input: '输入',
  Select: '选择',
  TreeSelect: '选择',
  Cascader: '选择',
  DataTimePicker: '选择'
}

/** 获取对应组件的placeholder */
export function getComponentPlaceholder(componentType: string, label: string) {
  return PLACEHOLDER_MAP[componentType] + label
}

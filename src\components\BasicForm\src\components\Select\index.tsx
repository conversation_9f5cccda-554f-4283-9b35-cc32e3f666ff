import { ElOption, ElSelect } from 'element-plus'
import { SelectEmits, selectProps, type SelectProps } from './types'
import { type SetupContext } from 'vue'

/**
 * 选择器
 */

export ddefineComponent({
  name: 'Select',
  props: selectProps,
  emits: ['change'],
  setup(props: SelectProps, { emit }: SetupContext<SelectEmits>) {
    const options = computed(() => props.options)
    return (
      <ElSelect onChange={(value) => emit('change', value)}>
        {{
          default: () =>
            options.value.map((item: { label: string; value: any }) => (
              <ElOption key={item.value} label={item.label} value={item.value} />
            ))
        }}
      </ElSelect>
    )
  }
})

// export default function SelectComp(props: SelectProps, { emit }: SetupContext<SelectEmits>) {
//   const options = computed(() => props.options)

//   return (
//     <ElSelect onChange={(value) => emit('change', value)}>
//       {{
//         default: () =>
//           options.value.map((item: { label: string; value: any }) => (
//             <ElOption key={item.value} label={item.label} value={item.value} />
//           ))
//       }}
//     </ElSelect>
//   )
// }

// SelectComp.props = selectProps
// SelectComp.emits = ['change']

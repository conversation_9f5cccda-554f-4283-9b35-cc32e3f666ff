<template>
  <div class="selected-person">
    <h3 class="title">{{ title }}</h3>

    <BasicTable ref="basicTable" :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import type { ITableSchema } from '@/components/BasicTable/types'
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import type { IPerson } from '../types'
import { cloneDeep } from 'lodash-es'

defineOptions({
  name: 'SelectedPerson'
})

const props = defineProps<Partial<ITableSchema>>()

const emit = defineEmits<{
  (e: 'selection-change', selected: IPerson[]): void
  (e: 'copy-auth'): void
}>()

const basicTable = ref<IBasicTableExpose>()

const selectedRollback = ref<IPerson[]>([])

/* 表格配置 */
const tableConfig: ITableSchema = {
  rowKey: props.rowKey ?? 'userId',
  columns: props.columns ?? [],
  tableCardStyle: {
    body: {
      padding: '0'
    }
  },
  enableSearch: true,
  allowSelection: true,
  selectionChange(selected) {
    selectedRollback.value = selected
    emit('selection-change', selected)
  },
  searchFormSchema: {
    formItems: [
      {
        field: 'copy',
        component: 'Button',
        disabled: () => selectedRollback.value.length === 0,
        componentProps: {
          content: '复制权限',
          type: 'primary',
          icon: 'ep:copy-document',
          onClick() {
            emit('copy-auth')
          }
        }
      }
    ],
    hiddenReset: true,
    hiddenSearch: true
  },
  checkboxProps: {
    reserveSelection: true
  },
  paginationProps: {
    pageSize: 15,
    pagerCount: 5,
    layout: 'total, prev, pager, next'
  },
  ortherHeight() {
    const titleHeight = 42
    const searchHeight = 69
    const cardPadding = 20
    return titleHeight + searchHeight + cardPadding
  }
}

watch(
  () => props.data,
  () => {
    const clonePerson: IPerson[] = unref(cloneDeep(props.data)) ?? []
    clonePerson.forEach((item) => {
      if (item.fingerprintInfoList) {
        item.fingerprintInfoList.forEach((fingerprint) => {
          fingerprint.status = 0
        })
      }
    })
    tableConfig.data = clonePerson
    basicTable.value?.getList()
    checkSelectedPerson()
  }
)

/** 勾选已选中人员 */
const checkSelectedPerson = () => {
  nextTick(() => {
    const tableData = unref(tableConfig.data)
    tableData?.forEach((row) => {
      basicTable.value?.toggleRowSelection(row, true)
    })
  })
}

/* 回退人员 */
const rollbackPerson = () => {
  const tableData = unref(tableConfig.data)
  console.log('🚀 ~ rollbackPerson ~ selectedRollback.value ====> ', selectedRollback.value)

  /* 更新选中状态 */
  if (tableData?.length) {
    for (const row of selectedRollback.value) {
      basicTable.value?.toggleRowSelection(row, false)
    }
  } else {
    basicTable.value?.clearSelection()
  }

  tableConfig.data = tableData?.filter(
    (item) => !selectedRollback.value.some((selected) => selected.userId === item.userId)
  )

  basicTable.value?.getList()
}

defineExpose({
  rollbackPerson,
  checkSelectedPerson
})
</script>

<style lang="scss" scoped>
.selected-person {
  height: 100%;

  .title {
    margin-bottom: 10px;
    font-size: 22px;
    font-weight: 400;
    color: #409eff;
  }

  &:deep(.el-pagination) {
    padding-right: 15px;
  }

  .basic-table {
    height: 100%;

    &:deep(.table-search) {
      .table-search__body {
        padding: 10px !important;
      }

      .el-form {
        height: auto;

        .el-form-item {
          margin-right: 8px;
          margin-bottom: 0;
        }
      }
    }

    &:deep(.el-card.table-area) {
      height: calc(100% - 110px);

      .el-card__body {
        height: 100%;

        .el-table {
          display: flex;

          &__inner-wrapper {
            width: 100%;

            &::before {
              width: 0;
            }
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="auth-user">
    <BasicTable :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { BasicTable } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'
import { authPersonalListAPI } from '@/api/synthesize-search'
import { DICT_TYPE, getDictObj } from '@/utils/dict'
import * as DeptApi from '@/api/system/dept'
defineOptions({
  name: 'UserAuth'
})

/* 路由导航 */
const router = useRouter()

/* 表格配置 */
const tableConfig: ITableSchema = {
  columns: [
    {
      label: '用户名',
      prop: 'username',
      enableSearch: true
    },
    {
      label: '部门',
      prop: 'deptName',
      enableSearch: true,
      searchFormItemProps: {
        field: 'deptId',
        component: 'Cascader',
        componentProps: {
          showAllLevels: false,
          props: {
            emitPath: false
          }
        },
        options: DeptApi.getSimpleDeptList.bind(null, {
          systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
        })
      }
    },
    {
      label: '性别',
      prop: 'sex',
      dictTag: true,
      dictType: DICT_TYPE.SYSTEM_USER_SEX
    }
  ],
  apiFn: authPersonalListAPI,

  showActions: true,
  actionsColumn: {
    actions: [
      {
        text: '查看权限',
        name: 'auth',
        link: true,
        permission: ['synthesis-search:user-auth:view'],
        onClick(scope) {
          router.push({
            path: '/synthesis-search/user-auth-detail',
            query: {
              id: scope.row.id
            }
          })
        }
      }
    ]
  }
}
</script>

<style lang="scss" scoped></style>

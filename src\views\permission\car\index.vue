<template>
  <div class="permission-car">
    <BasicTable ref="tableRef" :table-config="tableConfig">
      <template #car-number="{ row }">{{ row.province + row.carNumber }}</template>
    </BasicTable>

    <!-- 删除确认框 -->
    <RemoveBox ref="removeFaceBoxRef" :api-fn="carManageDeleteAPI" @removed="tableRef?.refresh" />

    <!-- 新增/修改车辆信息对话框 -->
    <EditCarDialog ref="editCarDialog" @edit-car="tableRef?.refresh" />

    <!-- 冻结解冻卡片确认框 -->
    <FreezeBox
      ref="freezeBoxRef"
      :api-fn="freezeCarAPI"
      status-field="status"
      row-field="carNumber"
      @freezed="tableRef?.refresh"
    />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import { ITableSchema } from '@/components/BasicTable/types'
import * as DeptApi from '@/api/system/dept'
import { carManageListAPI, carManageDeleteAPI, freezeCarAPI } from '@/api/permission'
import RemoveBox from '../components/RemoveBox.vue'
import EditCarDialog from '../components/EditCarDialog.vue'
import { DICT_TYPE, getDictObj } from '@/utils/dict'
import * as _ from 'lodash-es'
import FreezeBox from '../components/FreezeBox.vue'

defineOptions({
  name: 'PermissionCar'
})

/* 表格组件实例 */
const tableRef = ref<IBasicTableExpose>()

/* 删除确认框组件实例 */
const removeFaceBoxRef = ref<InstanceType<typeof RemoveBox>>()

/* 冻结/解冻卡片box组件实例 */
const freezeBoxRef = ref<InstanceType<typeof FreezeBox>>()

/* 新增车辆对话框组件实例 */
const editCarDialog = ref<InstanceType<typeof EditCarDialog>>()

/* 表格配置项 */
const tableConfig: ITableSchema = {
  columns: [
    {
      label: '车主',
      prop: 'username',
      enableSearch: true,
      searchFormItemProps: {
        placeholder: '请输入姓名'
      }
    },
    {
      label: '部门',
      prop: 'deptName',
      enableSearch: true,
      searchFormItemProps: {
        field: 'deptId',
        component: 'Cascader',
        componentProps: {
          showAllLevels: false,
          props: {
            emitPath: false
          }
        },
        options: DeptApi.getSimpleDeptList.bind(null, {
          systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
        })
      }
    },
    {
      label: '车牌号码',
      prop: 'carNumber',
      slot: 'car-number'
    },
    {
      label: '状态',
      prop: 'status',
      dictTag: true,
      dictType: DICT_TYPE.CARD_STATUS
    }
  ],
  beforeFetch() {
    return {
      systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
    }
  },
  apiFn: carManageListAPI,

  toolbar: [
    {
      text: '新增',
      name: 'add',
      type: 'primary',
      icon: 'ep:plus',
      onClick: () => {
        editCarDialog.value?.open({ title: '新增车辆', isUpdate: false })
      }
    }
  ],
  showActions: true,
  actionsColumn: {
    limit: 2,
    actions: [
      {
        text: '修改',
        textIndexField: 'status',
        name: 'update',
        type: 'primary',
        permission: ['permission:car:update'],
        onClick({ row }) {
          editCarDialog.value?.open({ title: '编辑车辆', isUpdate: true, row })
        }
      },
      {
        text({ row }) {
          if (row.status === 1) {
            return '冻结'
          } else if (row.status === 2) {
            return '解冻'
          } else {
            return false
          }
        },
        textIndexField: 'status',
        permission: ['permission:car:freeze'],
        name: 'freezeCar',
        type({ row }) {
          if (row.status === 1) {
            return 'warning'
          } else if (row.status === 2) {
            return 'success'
          } else {
            return 'info'
          }
        },
        onClick({ row }) {
          console.log(row)
          freezeBoxRef.value?.openConfirmBox(row)
        },
        disabled({ row }) {
          return row.status === 0
        }
      },
      {
        text: '删除',
        textIndexField: 'status',
        permission: ['permission:car:delete'],
        name: 'removeFace',
        type: 'danger',
        onClick({ row }) {
          console.log(row)
          removeFaceBoxRef.value?.openConfirmBox(row)
        }
        // disabled({ row }) {
        //   return row.status !== 1
        // }
      }
    ]
  }
}
</script>

<style lang="scss" scoped></style>

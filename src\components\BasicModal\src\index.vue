<template>
  <div class="base-dialog">
    <Dialog v-bind="getBindValue" v-model="dialogVisible">
      <slot></slot>
      <template #footer>
        <template v-if="footerVisible">
          <slot
            name="footer"
            :confirm-loading="confirmLoading"
            :cancel-loading="cancelLoading"
            v-if="$slots.footer"
          ></slot>
          <template v-else>
            <el-button type="primary" :loading="confirmLoading" @click="handleConfirm">{{ confirmBtnText }}</el-button>
            <el-button @click="handleCancel">{{ cancelBtnText }}</el-button>
          </template>
        </template>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { useAttrs, getCurrentInstance } from 'vue'
import { Dialog } from '@/components/Dialog'
import type { IModalProps, IModalMethods } from '../types'

defineOptions({
  name: 'BasicModal'
})

const currentInstance = getCurrentInstance()

const emit = defineEmits<{
  (e: 'register', modalMethods: IModalMethods, uid: number)
  (e: 'confirm', event: Event): void
  (e: 'cancel', event: Event): void
}>()

const props = defineProps<{
  title?: string
  fullscreen?: boolean
  width?: string | number
  scroll?: boolean
  maxHeight?: string | number
  confirmBtnText?: string
  cancelBtnText?: string
}>()

/* 对话框可见性 */
const dialogVisible = ref(false)

/** 对话框底部可见性 */
const footerVisible = ref(true)

/** 底部确认按钮加载状态 */
const confirmLoading = ref(false)

/** 底部取消按钮加载状态 */
const cancelLoading = ref(false)

/* 获取所有绑定到组件上的属性 */
const getBindValue = computed(() => {
  const delAttrs = ['loading']

  const attrs = useAttrs()
  console.log('attrs', attrs)
  const bindValue = {
    ...attrs,
    ...props
  }

  console.log(bindValue)

  for (const key in bindValue) {
    if (delAttrs.includes(key)) {
      delete bindValue[key]
    }
  }

  console.log('props', props)

  return bindValue
})

/** 确认按钮文本 */
const confirmBtnText = computed(() => {
  return props.confirmBtnText ?? '确定'
})

/** 取消按钮文本 */
const cancelBtnText = computed(() => {
  return props.cancelBtnText ?? '取消'
})

/* 设置对话框的属性 */
const setModalProps = (props: IModalProps) => {
  console.log('props ====>', props)
  if (Object.hasOwn(props, 'visible')) {
    dialogVisible.value = !!props.visible
  }
  if (Object.hasOwn(props, 'footerVisible')) {
    footerVisible.value = !!props.footerVisible
  }
  if (Object.hasOwn(props, 'confirmLoading')) {
    confirmLoading.value = !!props.confirmLoading
  }
  if (Object.hasOwn(props, 'cancelLoading')) {
    cancelLoading.value = !!props.cancelLoading
  }
}

/* 对话框的所有操作 */
const modalMethods: IModalMethods = {
  setModalProps,
  emitVisible: undefined
}

/* 确认按钮操作 */
const handleConfirm = (e: Event) => {
  emit('confirm', e)
}
/* 取消按钮操作 */
const handleCancel = (e: Event) => {
  dialogVisible.value = false
  emit('cancel', e)
}

/* 监听对话框可见性 */
watch(dialogVisible, (val) => {
  console.log(val)
  currentInstance && modalMethods.emitVisible?.(val, currentInstance.uid)
})

/* 注册 */
if (currentInstance) {
  emit('register', modalMethods, currentInstance.uid)
}
</script>

<style lang="scss" scoped></style>

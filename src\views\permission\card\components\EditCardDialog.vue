<template>
  <div class="edit-card-dialog">
    <Dialog :title="title" v-model="dialogVisible">
      <BasicForm ref="formRef" :form-schema="formSchema" />
      <template #footer>
        <el-button type="primary" :loading="loading" @click="handleSubmit">确认</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { BasicForm, type IBasicFormExpose } from '@/components/BasicForm'
import type { IFormSchema } from '@/components/BasicForm/types'
import { getAllUser } from '@/api/system/user'
import { addCardAPI } from '@/api/permission'
import { useMessage } from '@/hooks/web/useMessage'

defineOptions({
  name: 'EditCardDialog'
})

const emit = defineEmits<{
  (e: 'submit:success'): void
}>()

const message = useMessage()

/* 对话框标题 */
const title = ref('')

/* 对话框可见性 */
const dialogVisible = ref(false)

/* 按钮加载状态 */
const loading = ref(false)

/* 表单组件实例 */
const formRef = ref<IBasicFormExpose>()

/* form表单配置 */
const formSchema: IFormSchema = {
  labelWidth: 60,
  formItems: [
    {
      field: 'userId',
      label: '姓名',
      component: 'Select',
      options: getAllUser.bind(null, { systemType: 0 }),
      placeholder: '请选择用户',
      componentProps: {
        optionsLabelField: 'username'
      },
      rules: {
        required: true,
        message: '请选择用户',
        trigger: 'change'
      }
    },
    {
      field: 'cardNumber',
      label: '卡号',
      component: 'Input',
      placeholder: '请输入卡号',
      componentProps: {
        type: 'number'
      },
      rules: {
        required: true,
        message: '请输入卡号',
        trigger: 'blur'
      }
    }
  ]
}

/* 打开对话框 */
const open = (params?: { isUpdate: boolean; data?: any }) => {
  const { isUpdate = false, data } = params || {}

  title.value = isUpdate ? '编辑卡片' : '新增卡片'

  dialogVisible.value = true
}

/* 提交表单 */
const handleSubmit = async () => {
  console.log('提交表单')
  if (!formRef.value) return
  try {
    const { isValid, formData } = await formRef.value?.customValidate<{ userId: number; cardNumber: number }>()

    if (!isValid) return
    loading.value = true
    await addCardAPI(formData)
    emit('submit:success')
    message.success('操作成功')
    dialogVisible.value = false
  } catch (error) {
    console.log(error)
    message.error('操作失败')
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>

<template>
  <Dialog v-model="dialogVisible" title="配置资源" @close="handleClose">
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="控制器" name="door">
        <RoleDoorTab ref="doorTabRef" :role-id="roleId" :facilities="facilityData.door" />
        <!-- <keep-alive>
            <RoleDoorTab v-show="activeTab === 'door'" ref="doorTabRef" :role-id="roleId" :facilities="facilityData.door"/>
          </keep-alive> -->
      </el-tab-pane>

      <el-tab-pane label="人脸识别" name="face">
        <RoleFaceTab ref="faceTabRef" :role-id="roleId" :facilities="facilityData.face" />
      </el-tab-pane>
      <el-tab-pane label="车辆识别" name="car">
        <RoleCarTab ref="carTabRef" :role-id="roleId" :facilities="facilityData.car" />
      </el-tab-pane>
      <el-tab-pane label="智能锁" name="smartlock">
        <RoleSmartlockTab ref="smartlockTabRef" :role-id="roleId" :facilities="facilityData.smartlock" />
      </el-tab-pane>
      <el-tab-pane label="访客二维码" name="qrcode">
        <RoleQrcodeTab ref="qrcodeTabRef" :role-id="roleId" :facilities="facilityData.qrcode" />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <el-button :disabled="loading" type="primary" @click="submitPermissions" :loading="loading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as RoleApi from '@/api/system/role'
import RoleDoorTab from './RoleDoorTab.vue'
import RoleFaceTab from './RoleFaceTab.vue'
import RoleCarTab from './RoleCarTab.vue'
import RoleQrcodeTab from './RoleQrcodeTab.vue'
import RoleSmartlockTab from './RoleSmartlockTab.vue'
import * as FacilityApi from '@/api/system/facility'

const dialogVisible = ref(false) // 弹窗的是否展示
// const treeLoading=ref(false) //资源加载
const activeTab = ref('door')
const roleId = ref()
const doorTabRef = ref<InstanceType<typeof RoleDoorTab>>() // 门禁菜单树组件 Ref
const faceTabRef = ref<InstanceType<typeof RoleFaceTab>>() // 人脸菜单树组件 Ref
const carTabRef = ref<InstanceType<typeof RoleCarTab>>() // 车辆菜单树组件 Ref
const smartlockTabRef = ref<InstanceType<typeof RoleSmartlockTab>>() // 智能锁菜单树组件 Ref
const qrcodeTabRef = ref<InstanceType<typeof RoleQrcodeTab>>() // 二维码菜单树组件 Ref
const loading = ref(false) // 提交加载状态 禁止重复提交

interface IFacilityData {
  door: IData[]
  face: IData[]
  car: IData[]
  qrcode: IData[]
  smartlock:IData[]
}

interface IData {
  createTime: string
  updateTime: string
  creator: null
  updater: null
  deleted: boolean
  id: number
  deviceName: string
  deviceId: null
  cardReaderId: null
  parentId: number
  type: number
  status: number
  children: Child[]
}

interface Child {
  createTime: string
  updateTime: string
  creator: null
  updater: null
  deleted: boolean
  id: number
  deviceName: string
  deviceId: null | number
  cardReaderId: null | number
  parentId: number
  type: number
  status: number
}

// 父组件
const facilityData = ref<IFacilityData>({
  door: [],
  face: [],
  car: [],
  smartlock:[],
  qrcode:[]
})

// 加载设备数据（带缓存）
let cachedFacilityData: null | typeof facilityData.value = null
const loadFacilityData = async () => {
  // if (!cachedFacilityData) {
  const res = await FacilityApi.getFacilityMenuList()
  console.log('🚀 ~ loadFacilityData ~ res ====> ', res)
  facilityData.value = {
    // door: handleTree(res.filter((item) => item.type === 0)),
    door: res.filter((item) => item.type === 0),
    face: res.filter((item) => item.type === 1),
    car: res.filter((item) => item.type === 2),
    smartlock: res.filter((item) => item.type === 3),
    qrcode:res.filter((item) => item.type === 4)
  }
  console.log('🚀 ~ loadFacilityData ~ cachedFacilityData ====> ', cachedFacilityData)
  // }
  // facilityData.value = cachedFacilityData
}

/** 打开弹窗 */
const open = async (row: RoleApi.RoleVO) => {
  activeTab.value = 'door'
  roleId.value = row.id

  // if(doorTabRef.value){
  //   await doorTabRef.value.doorTabLoading()
  // }
  await loadFacilityData()
  dialogVisible.value = true

  console.log(facilityData.value.door)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/* 弹窗关闭事件处理函数 */
const handleClose = () => {
  doorTabRef.value?.resetForm()
  faceTabRef.value?.resetForm()
  smartlockTabRef.value?.resetForm()
  qrcodeTabRef.value?.resetForm()
}

const submitPermissions = async () => {
  loading.value = true
  try {
    const doorNeedsUpdate = doorTabRef.value?.isDoorUpdateStatus
    const faceNeedsUpdate = faceTabRef.value?.isFaceUpdateStatus
    const carNeedsUpdate = carTabRef.value?.isCarUpdateStatus
    const smartlockNeedsUpdate = smartlockTabRef.value?.isSmartlockUpdateStatus
    const qrcodeNeedsUpdate = qrcodeTabRef.value?.isQrcodeUpdateStatus
    const promises: Promise<any>[] = []
    if (doorNeedsUpdate) {
      promises.push(doorTabRef.value?.submitRoleDoorPermissions() as Promise<any>)
    }
    if (faceNeedsUpdate) {
      promises.push(faceTabRef.value?.submitRoleFacePermissions(roleId.value) as Promise<any>)
    }
    if (carNeedsUpdate) {
      promises.push(carTabRef.value?.submitRoleCarPermissions(roleId.value) as Promise<any>)
    }
    if (smartlockNeedsUpdate) {
      promises.push(smartlockTabRef.value?.submitRoleSmartlockPermissions(roleId.value) as Promise<any>)
    }
    if (qrcodeNeedsUpdate) {
      promises.push(qrcodeTabRef.value?.submitRoleQrcodePermissions(roleId.value) as Promise<any>)
    }

    await Promise.allSettled(promises)
    dialogVisible.value = false
  } finally {
    loading.value = false
  }
}

// watch(activeTab, async (newVal) => {
//   await nextTick()
//   if (newVal === 'door' && doorTabRef.value) {
//     await doorTabRef.value.doorTabLoading()
//   }else if (newVal === 'face' && faceTabRef.value) {
//     await faceTabRef.value.faceTabLoading()
//   }
// })
</script>

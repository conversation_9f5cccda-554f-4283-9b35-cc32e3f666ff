<template>
  <div class="edit-fingerprint-dialog">
    <Dialog :title="title" v-model="dialogVisible" @close="handleClose">
      <!-- 表单信息 -->
      <BasicForm ref="formRef" :form-schema="formSchema" :form-rules="formRules">
        <template #car-number="{ formData }">
          <!-- 省选择 -->
          <el-select
            class="car-province"
            v-model="numberPlateInfo.province"
            filterable
            default-first-option
            placeholder="省份"
          >
            <el-option
              :key="province.value"
              :label="province.label"
              :value="province.value"
              v-for="province in getDictOptions(DICT_TYPE.NUMBER_PLATE_PROVINCE)"
            />
          </el-select>
          <!-- 市对应字母选择 -->
          <el-select class="car-city" v-model="numberPlateInfo.city" filterable default-first-option placeholder="">
            <el-option
              :key="latter.value"
              :label="latter.label"
              :value="latter.value"
              v-for="latter in getDictOptions(DICT_TYPE.EN_LETTER)"
            />
          </el-select>
          <!-- 号码输入 -->
          <el-input class="num-input" v-model="inputs.num0" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num1" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num2" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num3" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num4" :maxlength="1" placeholder="" />
          <el-input
            v-if="formData.carType === 2"
            class="num-input"
            v-model="inputs.num5"
            :maxlength="1"
            placeholder=""
          />
        </template>
      </BasicForm>
      <!-- 对话框底部取消、确认按钮 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
        </span>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { Dialog } from '@/components/Dialog'
import { BasicForm } from '@/components/BasicForm'
import type { IBasicFormExpose, IFormSchema } from '@/components/BasicForm/types'
import * as UserAPI from '@/api/system/user'
import { getDictOptions, DICT_TYPE, getDictObj, getIntDictOptions } from '@/utils/dict'
import * as DeptApi from '@/api/system/dept'
import { carManageAddAPI, carManageEditAPI } from '@/api/permission'
import type { ICarListItem } from '@/api/permission/types'
import type { ICascaderOption } from '@/components/BasicForm/types'

defineOptions({
  name: 'EditFingerPrintDialog'
})

/* 将列表数据构造cascaderoptions所需要的树结构数据 */
function structureCasOptions(arr: any[], parentId = 0) {
  let options: ICascaderOption[] = []

  for (const item of arr) {
    const option: ICascaderOption = {
      label: item.name,
      value: item.id,
      children: []
    }

    if (item.parentId === parentId) {
      let children = structureCasOptions(arr, item.id)
      if (children.length) {
        option.children = children
      }
      options.push(option)
    }
  }

  return options
}

const emit = defineEmits<{
  (event: 'edit-car'): void
}>()

/* 表单字段校验规则 */
const formRules = {
  systemType: [{ required: true, message: '请选择所属系统', trigger: 'change' }],
  userId: [{ required: true, message: '请选择用户', trigger: 'change' }],
  deptId: [{ required: true, message: '请选择部门', trigger: 'change' }],
  carType: [{ required: true, message: '请选择车牌类型', trigger: 'change' }]
}

/* 表单字段 */
const formSchema = reactive<IFormSchema>({
  rules: formRules,
  labelWidth: 80,
  formItems: [
    {
      field: 'systemType',
      label: '所属系统',
      component: 'Select',
      options: getIntDictOptions(DICT_TYPE.SYSTEM_TYPE),
      onChange: getDeptOptions,
      disabled() {
        return isUpdate.value
      }
    },
    {
      field: 'deptId',
      label: '部门',
      component: 'Cascader',
      placeholder: '请选择部门',
      componentProps: {
        showAllLevels: false,
        props: {
          emitPath: false
        }
      },
      options: [],
      onChange(value) {
        UserAPI.getAllUser({
          systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, formRef.value?.formData.systemType)?.value
        }).then((res) => {
          formSchema.formItems![2].options = res
            .filter((item) => item.deptId === value)
            .map((personal) => ({
              label: personal.username,
              value: personal.id
            }))
        })
      },
      disabled() {
        return isUpdate.value
      }
    },
    {
      component: 'Select',
      field: 'userId',
      label: '车主',
      placeholder: '请选择用户',
      options: [],
      disabled() {
        return isUpdate.value
      },
      componentProps: {
        filterable: true,
        optionsLabelField: 'username'
      }
    },
    {
      field: 'carType',
      component: 'Radio',
      defaultValue: 1,
      label: '车牌类型',
      options: getIntDictOptions(DICT_TYPE.NUMBER_PLATE_TYPE),
      colProps: {
        span: 24
      }
    },
    {
      field: 'carNumber',
      label: '车牌号码',
      colProps: {
        span: 24
      },
      slot: 'car-number'
    }
  ]
})

/* 表单组件实例 */
const formRef = ref<IBasicFormExpose>()

/* 对话框标题 */
const title = ref('新增车辆')
/* 对话框可见性 */
const dialogVisible = ref(false)
/* 按钮loading */
const loading = ref(false)
/* 是否修改 */
const isUpdate = ref(false)
/* 表格行数据 */
const row = ref<ICarListItem>()

/* 车牌号码信息 */
const numberPlateInfo = reactive({
  province: '',
  city: '',
  numbers: ''
})

/* 车牌号码输入框 */
let inputs = reactive<Record<string, any>>({
  num0: '',
  num1: '',
  num2: '',
  num3: '',
  num4: '',
  num5: ''
})

/* 打开对话框 */
const open = async (params) => {
  title.value = params.title
  isUpdate.value = params.isUpdate
  row.value = params.row
  dialogVisible.value = true

  if (params.isUpdate) {
    getDeptOptions(params.row.systemType)
    const res = await UserAPI.getAllUser({
      systemType: params.row.systemType
    })
    formSchema.formItems![2].options = res.map((personal) => ({
      label: personal.username,
      value: personal.id
    }))
    console.log(formSchema.formItems![2].options)
    nextTick(() => {
      formRef.value?.setFormFieldsValue({
        systemType: params.row.systemType,
        userId: params.row.userId,
        deptId: params.row.deptId,
        carNumber: params.row.carNumber,
        carType: params.row.carType
      })

      numberPlateInfo.province = params.row.province
      numberPlateInfo.city = params.row.carNumber.match(/[A-Z]+/)[0]
      numberPlateInfo.numbers = params.row.carNumber.match(/[0-9]+/)[0]

      for (let i = 0; i < numberPlateInfo.numbers.length; i++) {
        inputs[`num${i}`] = numberPlateInfo.numbers[i]
      }
    })
  }
}

/** 获取部门 */
function getDeptOptions(value: number) {
  console.log('🚀 ~ getDeptOptions ~ value ====> ', value)
  DeptApi.getSimpleDeptList({
    systemType: value
  })
    .then((res) => {
      console.log('🚀 ~ .then ~ res ====> ', res)

      formSchema.formItems![1].options = structureCasOptions(res)
      console.log('🚀 ~ .then ~ formSchema.formItems![1].options ====> ', formSchema.formItems![1].options)
    })
    .catch((error) => {
      console.log(error)
    })
}

/* 关闭对话框 */
const close = () => {
  dialogVisible.value = false
}

/* 关闭对话框的事件 */
const handleClose = () => {
  numberPlateInfo.city = ''
  numberPlateInfo.province = ''
  numberPlateInfo.numbers = ''
  Object.keys(inputs).forEach((key) => {
    inputs[key] = ''
  })
}

/* 提交 */
const handleSubmit = () => {
  const numbers = Object.values(inputs).join('')
  const numberPlate = `${numberPlateInfo.city}${numbers}`
  formRef.value?.customValidate?.((isValid, formData) => {
    if (!isValid) return
    loading.value = true

    console.log('🚀 ~ handleSubmit ~ formData ====> ', formData)

    const params = {
      id: row.value?.id,
      ...formData,
      province: numberPlateInfo.province,
      carNumber: numberPlate
    }

    const apiFn = isUpdate.value ? carManageEditAPI : carManageAddAPI

    apiFn(params)
      .then(() => {
        ElMessage.success('操作成功！')
        emit('edit-car')
        close()
      })
      .catch((error) => {
        console.log(error)
      })
      .finally(() => {
        loading.value = false
      })
  })
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.edit-fingerprint-dialog {
  &:deep(.el-card__body) {
    padding: 0 !important;
  }

  .car-province {
    --el-select-width: 70px;

    min-width: 70px !important;
  }

  .car-province {
    & + .car-city {
      --el-select-width: 60px;

      min-width: 60px;
      margin-left: 10px;
    }
  }

  .num-input {
    --el-input-width: 40px;

    min-width: 40px !important;
    margin-left: 10px;

    &:deep(.el-input__inner) {
      text-align: center;
    }
  }
}
</style>

import PersonShuttle from '../components/ShuttleTable/PersonShuttle.vue'
import ReaderShuttle from '../components/ShuttleTable/ReaderShuttle.vue'
import CopyUserReader from '../components/Dialog/CopyUserReader.vue'
import Authorisation from '../components/Dialog/Authorisation.vue'
import CopyReaderUser from '../components/Dialog/CopyReaderUser.vue'
import { IReader, IPerson } from '../types'
import { useRoute } from 'vue-router'
import { authorisationAPI } from '@/api/authorization'

export function useShuttle() {
  /* 路由信息对象 */
  const route = useRoute()
  /* 授权类型 */
  const type = (route.meta.query as { type: string })?.type
  /* 授权对话框组件实例 */
  const authorisationRef = ref<InstanceType<typeof Authorisation>>()
  /* 人员穿梭框组件实例对象 */
  const personShuttleRef = ref<InstanceType<typeof PersonShuttle>>()
  /* 复制用户权限对话框组件实例 */
  const copyUserReaderRef = ref<InstanceType<typeof CopyUserReader>>()
  /* 读卡器穿梭狂组件实例 */
  const readerShuttleRef = ref<InstanceType<typeof ReaderShuttle>>()
  /* 复制权限对应的用户对话框组件实例 */
  const copyReaderUserRef = ref<InstanceType<typeof CopyReaderUser>>()

  /* ========== 复制用户权限 ========== */

  /* 复制人员拥有的权限 */
  const handleCopyReaderAuth = (userInfo) => {
    console.log('复制人员拥有的权限', userInfo)
    copyUserReaderRef.value?.openDialog(userInfo)
  }
  /* 提交用户选择的权限 */
  const handleSubmitSelectedReader = (selectedReader: IReader[]) => {
    if (!readerShuttleRef.value) return

    /** 已选读卡器列表中的数据 */
    const selectedReaders = readerShuttleRef.value.getSelectedReaders()

    const selected = [...selectedReader, ...selectedReaders].reduce(
      (arr, current) => {
        const isExist = arr.some((item) => validateReaderIsSame(item, current))
        if (!isExist) {
          arr.push(current)
        }
        return arr
      },
      [...selectedReaders]
    )

    /** 设置已选读卡器 */
    readerShuttleRef.value.setSelectedReader(selected)

    /* 复制的设备在源数据表中的勾选状态改为勾选状态 */

    const basicTableRef = readerShuttleRef.value.readerRef?.basicTableRef
    const tableData = basicTableRef?.getTableDataSource()

    tableData?.forEach((item: any) => {
      selectedReaders.forEach((selected) => {
        if (validateReaderIsSame(item, selected)) {
          /* el-table的toggleRowSelection(row, true/false)方法 中的 row 只能是绑定到 table 中的 list 中的数据 */
          basicTableRef?.toggleRowSelection(item, true)
        }
      })
    })
    copyUserReaderRef.value?.closeDialog()
  }

  /* ========== 复制权限对应的用户 ========== */

  /* 复制读卡器对应人员权限 */
  const handleCopyPersonAuth = (readerInfo) => {
    copyReaderUserRef.value?.openDialog(readerInfo)
  }

  /* 提交选择的权限对应的用户 */
  const handleSubmitSelectedUser = (selectedUserInfo) => {
    if (!personShuttleRef.value) return

    const selectedPersons = personShuttleRef.value.getSelectedPersons()

    /** 复制的人员 */
    const selectedPerson = selectedUserInfo.reduce(
      (arr, current) => {
        const isExist = arr.some((item) => validatePersonIsSame(item, current))
        if (!isExist) {
          arr.push(current)
        }
        return arr
      },
      [...selectedPersons]
    )

    /** 设置到已选人员列表中 */
    personShuttleRef.value.setSelectedPerson(selectedPerson)

    /* 被添加到已选表格中的人员在源数据表中的勾选状态改为勾选状态 */

    const basicTableRef = personShuttleRef.value.personRef?.basicTableRef

    const tableData = basicTableRef?.getTableDataSource()

    tableData?.forEach((item: any) => {
      selectedUserInfo.forEach((selected) => {
        let isExist = false
        if (type === '5') {
          isExist = item.id === selected.authorizeId || item.id === selected.id
        } else {
          isExist = item.userId === selected.userId || item.userId === selected.authorizeId
        }
        if (isExist) {
          /* el-table的toggleRowSelection(row, true/false)方法 中的 row 只能是绑定到 table 中的 list 中的数据 */
          basicTableRef?.toggleRowSelection(item, isExist)
        }
      })
    })

    copyReaderUserRef.value?.closeDialog()
  }

  /* ========= 授权 ========= */

  /* 授权按钮点击事件 */
  const handleAuthOrisation = () => {
    validateSelected(({ readers, persons }) => {
      const authorizeIdList = getAuthIdList(persons)

      const data = {
        ids: authorizeIdList,
        readerIds: readers.map((reader) => reader.id ?? reader.authorizedId),
        systemType: persons[0].systemType
      }
      authorisationRef.value?.openDialog(data)
    })
  }

  /* ========= 解权 ========= */
  const handleRemoveAuth = () => {
    validateSelected(({ readers, persons }) => {
      const authorizeIdList = getAuthIdList(persons)

      ElMessageBox.confirm('确认解除人员对应的权限吗？', '提示!', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          const data = {
            type: 1,
            authorizeIdList,
            authorizedIdList: readers.map((reader) => reader.id ?? reader.authorizedId),
            systemType: persons[0].systemType,
            authorizeType: +type
          }

          authorisationAPI(data).then(() => {
            ElMessage.success('已解除选择的人员对应的权限!')
            /* 已选读卡器列表中过滤掉回退的读卡器 */

            if (!readerShuttleRef.value) return

            /** 已选读卡器列表中的数据 */
            const selectedReaders = readerShuttleRef.value.getSelectedReaders()

            const selectedReader = selectedReaders.filter((selected) => {
              return !readers.some((reader) => validateReaderIsSame(reader, selected))
            })

            /** 设置已选读卡器 */
            readerShuttleRef.value.setSelectedReader(selectedReader)

            readerShuttleRef.value?.selectedReaderRef?.rollbackReader()

            /* 解权的设备在源数据表中的勾选状态改为取消状态 */

            const basicTableRef = readerShuttleRef.value.readerRef?.basicTableRef

            const tableData = basicTableRef?.getTableDataSource()

            tableData?.forEach((item: any) => {
              readers.forEach((selected) => {
                if (item.id === selected.authorizedId || item.id === selected.id) {
                  /* el-table的toggleRowSelection(row, true/false)方法 中的 row 只能是绑定到 table 中的 list 中的数据 */
                  basicTableRef?.toggleRowSelection(item, false)
                }
              })
            })
          })
        })
        .catch(() => {
          ElMessage.info('解权操作取消')
        })
    })
  }

  /* ========== 验证是否选择了人员以及权限 ========== */
  function validateSelected(callback: (data: { readers: IReader[]; persons: IPerson[] }) => void) {
    /* 已选择的人员和权限 */
    const readers = readerShuttleRef.value?.getRollbackReaders() ?? []
    const persons = personShuttleRef.value?.getRollbackPersons() ?? []
    console.log('🚀 ~ validateSelected ~ personShuttleRef.value?.getRollbackPersons() ====> ', personShuttleRef.value?.getRollbackPersons())

    if (!readers.length && !persons.length) {
      return ElMessage.warning('请选择需要授权的人员以及读卡器！')
    } else if (!readers.length) {
      return ElMessage.warning('请选择需要授权的读卡器！')
    } else if (!persons.length) {
      return ElMessage.warning('请选择需要授权的人员！')
    } else {
      callback({
        readers,
        persons
      })
    }
  }

  /* ========== 验证源数据表格和已选数据表格的读卡器是否为同一个读卡器 ========== */
  function validateReaderIsSame(source, selected) {
    if (typeof source.id !== 'undefined' && typeof selected.id !== 'undefined') {
      return source.id === selected.id
    } else if (typeof selected.authorizedId !== 'undefined' && typeof source.id !== 'undefined') {
      return selected.authorizedId === source.id
    } else {
      return false
    }
  }

  /* 验证源数据表格和已选数据表格的读卡器是否为同一人 */
  function validatePersonIsSame(source, selected) {
    return source.userId === selected.userId || source.userId === selected.authorizeId
  }

  /** 获取授权Id列表 */
  function getAuthIdList(persons: IPerson[]): number[] {
    let authorizeIdList: number[] = []
    if (type === '2') {
      authorizeIdList = persons.reduce<number[]>((result, current) => {
        console.log('🚀 ~ validateSelected ~ current ====> ', current)

        return [...result, ...current.checked]
      }, [])
    } else if (type === '1') {
      authorizeIdList = persons.map((person) => person.cardId)
    } else {
      authorizeIdList = persons.map((person) => person.id) as number[]
    }

    return authorizeIdList
  }

  return {
    type,
    authorisationRef,
    personShuttleRef,
    copyReaderUserRef,
    readerShuttleRef,
    copyUserReaderRef,
    handleCopyReaderAuth,
    handleSubmitSelectedReader,
    handleCopyPersonAuth,
    handleSubmitSelectedUser,
    handleAuthOrisation,
    handleRemoveAuth
  }
}

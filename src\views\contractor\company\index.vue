<template>
  <BasicTable ref="tableRef" :tableConfig="tableConfig" @reset="handleQuery" @search="handleQuery" />
  <!-- 表单弹窗：添加/修改 -->
  <UnitForm ref="formRef" @success="tableRef?.refresh" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions, getDictObj } from '@/utils/dict'
import { handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import UnitForm from './UnitForm.vue'
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'

defineOptions({ name: 'ContractorCompany' })

const tableRef = ref<IBasicTableExpose>()

const list = ref() // 列表的数据

const tableConfig: ITableSchema = {
  columns: [
    {
      label: '单位名称',
      prop: 'name',
      enableSearch: true
    },
    {
      label: '负责人',
      prop: 'leaderUserId'
    },
    {
      label: '排序',
      prop: 'sort'
    },
    {
      label: '状态',
      prop: 'status',
      dictTag: true,
      dictType: DICT_TYPE.COMMON_STATUS,
      enableSearch: true,
      searchFormItemProps: {
        component: 'Select',
        options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
      }
    },
    {
      label: '创建时间',
      prop: 'createTime'
    }
  ],
  data: list,
  toolbar: [
    {
      name: 'add',
      text: '新增',
      icon: 'ep:plus',
      type: 'primary',
      onClick() {
        openForm('create')
      }
    },
    {
      name: 'expand',
      text: '展开/折叠',
      icon: 'ep:sort',
      type: 'danger',
      onClick() {
        tableRef.value?.toggleExpandAll()
      }
    }
  ],
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text: '编辑',
        name: 'edit',
        permission: ['contractor:company:edit'],
        onClick({ row }) {
          console.log(row)
          openForm('edit', row.id)
        }
      },
      {
        text: '删除',
        name: 'delete',
        type: 'danger',
        permission: ['contractor:company:delete'],
        onClick({ row }) {
          handleDelete(row.id)
        }
      }
    ]
  },
  paginationProps: {
    pageSize: 100
  }
}

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const queryParams = reactive({
  pageNo: 1,
  pageSize: 100,
  name: undefined,
  status: undefined,
  systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 1)?.value
})
const loading = ref(false) // 列表的加载中

/** 查询部门列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeptApi.getDeptPage(queryParams)
    list.value = handleTree(data)
    tableRef.value?.getList()
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = (searchData) => {
  console.log('🚀 ~ handleQuery ~ searchData ====> ', searchData)
  queryParams.name = searchData.name
  queryParams.status = searchData.status
  console.log(list.value)
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DeptApi.deleteDept({ id, systemType: 1 })
    message.success(t('common.delSuccess'))
    // 刷新列表
    tableRef.value?.refresh()
  } catch {}
}

/** 初始化 **/
getList()
</script>

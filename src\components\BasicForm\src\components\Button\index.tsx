import { SetupContext } from 'vue'
import { Icon } from '@/components/Icon'
import { buttonProps, type ButtonProps, type ButtonEmits } from './types'

export default function ButtonComp(props: ButtonProps, { emit, attrs }: SetupContext<ButtonEmits>) {
  console.log('🚀 ~ ButtonComp ~ attrs ====> ', attrs)
  /** attrs和props的属性进行合并 */
  const mergeProps = { ...attrs, ...props }

  console.log('🚀 ~ ButtonComp ~ mergeProps ====> ', mergeProps)

  return (
    <ElButton type={mergeProps.type ?? 'primary'} plain={mergeProps?.plain || true} onClick={() => emit('click')}>
      {mergeProps.icon && <Icon icon={mergeProps.icon} color={mergeProps.iconColor} />}
      {mergeProps.content && <span>{mergeProps.content}</span>}
    </ElButton>
  )
}

ButtonComp.props = buttonProps
ButtonComp.emits = ['click']

<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      ref="queryFormRef"
      :inline="true"
      :model="queryParams"
      class="-mb-15px"
      label-width="68px"
      @submit.prevent
    >

      <el-form-item label="设备名称" prop="equipmentName">
        <el-input
          v-model="queryParams.equipmentName"
          class="!w-240px"
          clearable
          placeholder="请输入设备名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="状态" prop="installationPosition">
        <el-input
          v-model="queryParams.installationPosition"
          class="!w-240px"
          clearable
          placeholder="请输入安装位置"
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item >
        <el-button @click="handleQuery">
          <Icon class="mr-5px" icon="ep:search" />
          搜索
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hardware:smartlock:create']"
        >
          <Icon icon="ep:plus" /> 新增
        </el-button>
      </el-form-item>

    </el-form>
  </ContentWrap>

  <!-- 数据列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :header-cell-style="{ backgroundColor: '#fafafa' }" :row-key="row => row.id">
      <!-- <el-table-column align="center" label="设备编号" prop="equipmentNumber" /> -->
      <el-table-column align="center" label="设备名称" prop="equipmentName" />
      <el-table-column align="center" label="设备IP" prop="equipmentIp" />
      <el-table-column align="center" label="设备端口" prop="equipmentPort" />
      <el-table-column align="center" label="安装位置" prop="installationPosition" show-overflow-tooltip width="260"/>
      <el-table-column align="center" label="状态" prop="onLine" >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.HARDWARE_OTHER_STATUS" :value="scope.row.onLine" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="类型(进/出)" prop="type" >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.HARDWARE_OTHER_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" :width="200">
      <template #default="scope">
        <div class="flex items-center justify-center">
          <el-button
            v-hasPermi="['hardware:smartlock:update']"
            link
            type="primary"
            @click="openForm('update',scope.row.id)"
          >
            编辑
          </el-button>
          <el-dropdown
            @command="(command) => handleCommand(command, scope.row)"
            v-hasPermi="[
              'hardware:smartlock:card',
              'hardware:smartlock:opendoor',
              'hardware:smartlock:closedoor',
              'hardware:smartlock:delete'
            ]"
          >
            <el-button type="primary" link><Icon icon="ep:d-arrow-right" /> 更多</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="openFaceAssignForm" v-if="checkPermi(['hardware:smartlock:card'])">
                  <Icon icon="fa:smile-o" />卡片管理
                </el-dropdown-item>
                <el-dropdown-item command="openFingerprintAssignForm" v-if="checkPermi(['hardware:smartlock:fingerprint'])">
                  <Icon icon="fa-solid:fingerprint" />指纹管理
                </el-dropdown-item>
                <el-dropdown-item command="handleOpenDoor" v-if="checkPermi(['hardware:smartlock:opendoor'])">
                  <Icon icon="fa-solid:door-open" />远程开门
                </el-dropdown-item>
                <!-- <el-dropdown-item command="handleCloseDoor" v-if="checkPermi(['hardware:smartlock:closedoor'])">
                  <Icon icon="fa-solid:door-closed" />关门
                </el-dropdown-item> -->
                <el-dropdown-item command="handleDelete" v-if="checkPermi(['hardware:smartlock:delete'])">
                  <Icon icon="ep:delete" />删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 智能锁：新增/修改 -->
  <HardwareForm ref="faceRef"  @success="getList"/>
  
  <!-- 智能锁：授权管理（卡片/指纹） -->
  <HardwareAssignForm ref="faceAssignRef" />
</template>

<script lang="ts" setup>

import { DICT_TYPE} from '@/utils/dict'
import * as HardwareApi from '@/api/hardware'
// import FaceForm from '@/views/hardware/face/FaceForm.vue'
// import FaceAssignForm from '@/views/hardware/face/FaceAssignForm.vue'
import HardwareForm from '../common/HardwareForm.vue'
import HardwareAssignForm from '../common/HardwareAssignForm.vue'
import { checkPermi } from '@/utils/permission'


defineOptions({ name: 'HardwareSmartlock' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading=ref(false) //列表加载中
const total=ref(0) //列表总页数
const list=ref([]) //列表数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  equipmentName: '',
  onLine:undefined,
  deviceType:[3]       //设备类型：3-智能锁 4-人脸识别机 5-车辆刀闸机
})

interface FaceVo  {
  id: number
  equipmentName: string
  installationPosition: string
  equipmentIp: string
  equipmentPort: number
}


const queryFormRef = ref() // 搜索的表单

/** 查询第三方人脸识别机信息列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await HardwareApi.getFaceOrCarDataList(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 操作分发 */
const handleCommand = (command: string, row: FaceVo) => {
  switch (command) {
    case 'openFaceAssignForm':
      
      openFaceAssignForm(1,0,row.id) //参数1授权类型：1-卡片 2-指纹 参数2系统类型 ：0-门禁系统 1-承包商系统 参数3人脸识别设备记录id
      break
    case 'openFingerprintAssignForm':
    
      openFaceAssignForm(2,0,row.id) //参数1授权类型：1-卡片 2-指纹 参数2系统类型 ：0-门禁系统 1-承包商系统 参数3人脸识别设备记录id
      break
    case 'handleOpenDoor':
      
      handleOpenDoor(row.equipmentIp)
      break
    // case 'handleCloseDoor':
     
    //   handleCloseDoor(row.equipmentIp)
    //   break
    case 'handleDelete':
      handleDelete(row.id,row.equipmentName)
      break
    default:
      break
  }
}


/** 修改数据 **/
const faceRef=ref()
const openForm=(type:string,id?: number)=>{
  faceRef.value.open(type,id,3)    //参数1-type：操作类型：add-新增；update-修改 参数2-id：修改记录id 参数3-设备类型：3-智能锁 4-人脸识别机 5-车辆刀闸机
}

/** 人脸卡片管理-指纹管理 */
const faceAssignRef=ref()
const openFaceAssignForm=(authorizeType:number,systemType:number,id: number)=>{
  faceAssignRef.value.open(authorizeType,systemType,id,Number(queryParams.deviceType)) //authorizeType授权类型：1 - 卡片管理；2 - 指纹管理；systemType系统类型：0-门禁 1-承包商 deviceType设备类型：3-智能锁 4-人脸识别机
}

/**远程开门 */
const handleOpenDoor=async(equipmentIp: string)=>{
  try {
    await message.strongConfirm('是否远程开门？', '系统提示', async () => {
      const response=await HardwareApi.controlDoor(equipmentIp,1)
      if (response.code === 0) {
        message.success('远程开门成功')
      } else {
        message.error('远程开门失败')
      }
    })
  } catch (error) {
    // console.log('格式化过程中出现问题' + error)
  }
}

/**远程关门 */
// const handleCloseDoor=async(equipmentIp: string)=>{
//   try {
//     await message.strongConfirm('是否远程关门？', '系统提示', async () => {
//       const response=await HardwareApi.controlDoor(equipmentIp,0)
//       if (response.code === 0) {
//         message.success('远程关门成功')
//       } else {
//         message.error('远程关门失败')
//       }
//     })
//   } catch (error) {
//     // console.log('格式化过程中出现问题' + error)
//   }
// }

/** 删除按钮操作 **/
const handleDelete=async (id:number,equipmentName:string)=>{
  try {
    // 删除的二次确认
    await message.strongConfirm(`是否删除${equipmentName}的设备？`, '系统提示', async () => {
      // 发起删除
      await HardwareApi.deleteFaceOrCarRecognition(id)
      message.success(t('common.delSuccess'))
      // 刷新列表
      await getList()
    })
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="edit-approval">
    <BasicModal :title="title" @register="register" @confirm="handleConfirm" @close="handleClose">
      <BasicForm ref="formRef" :form-schema="formSchema">
        <template #car-number="{ formData }">
          <!-- 省选择 -->
          <el-select
            class="car-province"
            v-model="numberPlateInfo.province"
            filterable
            default-first-option
            placeholder="省份"
          >
            <el-option
              :key="province.value"
              :label="province.label"
              :value="province.value"
              v-for="province in getDictOptions(DICT_TYPE.NUMBER_PLATE_PROVINCE)"
            />
          </el-select>
          <!-- 市对应字母选择 -->
          <el-select class="car-city" v-model="numberPlateInfo.city" filterable default-first-option placeholder="">
            <el-option
              :key="latter.value"
              :label="latter.label"
              :value="latter.value"
              v-for="latter in getDictOptions(DICT_TYPE.EN_LETTER)"
            />
          </el-select>
          <!-- 号码输入 -->
          <el-input class="num-input" v-model="inputs.num0" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num1" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num2" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num3" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num4" :maxlength="1" placeholder="" />
          <el-input
            v-if="formData.carType === 2"
            class="num-input"
            v-model="inputs.num5"
            :maxlength="1"
            placeholder=""
          />
        </template>
        <template #avatar="{ formData, field }">
          <img :src="avatar" alt="" class="avatar" v-if="isNoAdd" />
          <el-upload
            class="avatar-uploader"
            :auto-upload="false"
            action="#"
            list-type="picture-card"
            :on-change="(file) => handleFileChange(file, formData, field)"
            :on-remove="() => handleRemoveFile(formData, field)"
            v-else
          >
            <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </template>
      </BasicForm>
      <template #footer="{ confirmLoading, cancelLoading }" v-if="isNoAdd && !status">
        <el-button type="primary" :disabled="cancelLoading" :loading="confirmLoading" @click="handleConfirm"
          >同意</el-button
        >
        <el-button :disabled="confirmLoading" :loading="cancelLoading" @click="handleCancel">拒绝</el-button>
      </template>
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { BasicModal } from '@/components/BasicModal'
import { useModalInner } from '@/components/BasicModal/hooks/useModal'
import { BasicForm, IBasicFormExpose } from '@/components/BasicForm'
import type { IFormSchema } from '@/components/BasicForm/types'
import { getIntDictOptions, getDictOptions, DICT_TYPE } from '@/utils/dict'
import { createApprovalRecordAPI, agreeApprovalAPI, rejectApprovalAPI } from '@/api/visitor'
import type { IApprovalRecord } from '@/api/visitor/types'
import { getFaceOrCarDataList } from '@/api/hardware'
import { useMessage } from '@/hooks/web/useMessage'
import { getAllUser } from '@/api/system/user'
import { Plus } from '@element-plus/icons-vue'
import { getAccessToken } from '@/utils/auth'

defineOptions({
  name: 'EditApproval'
})

const emit = defineEmits<{
  (e: 'submit:success'): void
  (e: 'reject'): void
}>()

const message = useMessage()

/** 表单组件实例 */
const formRef = ref<IBasicFormExpose>()

/** 头像临时地址 */
const avatar = ref<string>('')

/** 是否驾车 */
const isDrive = reactive({
  value: 0
})

/** 确认按钮加载状态 */
// const confirmLoading = ref(false)

/** 是否为编辑或查看模式 */
const isNoAdd = ref(false)

/** 门数据 */
const doorOptions = ref<{ label: string; value: number }[]>([])

/** 审批记录的id */
const recordId = ref()

/** 审批状态 */
const status = ref()

/** 表单配置 */
const formSchema = reactive<IFormSchema>({
  labelWidth: '80px',
  formItems: [
    {
      field: 'visitorName',
      label: '访客姓名',
      component: 'Input',
      placeholder: '请输入访客姓名',
      rules: { required: true, message: '请输入访客姓名', trigger: 'blur' }
    },
    {
      field: 'sex',
      label: '性别',
      component: 'RadioGroup',
      defaultValue: 1,
      options: getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX),
      rules: { required: true, message: '性别为必填项', trigger: 'change' }
    },
    {
      field: 'phone',
      label: '联系方式',
      component: 'Input',
      placeholder: '请输入联系方式',
      rules: {
        required: true,
        validator(_rule, value, callback) {
          const phoneReg = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/
          if (!value) return callback(new Error('联系方式为必填项！'))
          if (!phoneReg.test(value)) return callback(new Error('联系方式格式不正确！'))
          callback()
        },
        trigger: 'blur'
      }
    },
    {
      field: 'userCompany',
      label: '来访单位',
      component: 'Input',
      placeholder: '请输入来访人单位',
      rules: { required: true, message: '来访人单位为必填项', trigger: 'blur' }
    },
    {
      field: 'userId',
      label: '受访人',
      component: 'Select',
      options: getAllUser.bind(null, { systemType: 0 }),
      componentProps: {
        optionsLabelField: 'username'
      },
      placeholder: '请选择受访人',
      rules: { required: true, message: '受访人姓名为必填项', trigger: 'change' }
    },
    {
      field: 'idCard',
      label: '身份证',
      component: 'Input',
      placeholder: '请输入身份证',
      rules: {
        required: true,
        validator(_rule, value, callback) {
          const idCard = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/

          if (!value) return callback(new Error('身份证为必填项！'))
          if (!idCard.test(value)) return callback(new Error('身份证格式不正确！'))
          callback()
        },
        trigger: 'blur'
      }
    },
    {
      field: 'startTime',
      label: '开始时间',
      component: 'DataTimePicker',
      placeholder: '请选择开始时间',
      componentProps: {
        type: 'datetime',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        disabledDate(date) {
          return date < new Date()
        }
      },
      rules: { required: true, message: '开始时间为必填项', trigger: 'change' }
    },
    {
      field: 'endTime',
      label: '结束时间',
      component: 'DataTimePicker',
      placeholder: '请选择结束时间',
      componentProps: {
        type: 'datetime',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        disabledDate(date) {
          return date < new Date()
        }
      },
      rules: { required: true, message: '结束时间为必填项', trigger: 'change' }
    },
    {
      field: 'visitorType',
      label: '访问方式',
      component: 'RadioGroup',
      defaultValue: 0,
      options: getIntDictOptions(DICT_TYPE.VISITOR_TYPE),
      rules: { required: true, message: '访问方式为必填项', trigger: 'change' },
      async onChange(value) {
        isDrive.value = value
        formSchema.formItems![9].hidden = value === 2
        formSchema.formItems![10].hidden = !(value === 0)
        formSchema.formItems![11].hidden = !(value === 1)
        formSchema.formItems![12].hidden = !(value === 1)
      }
    },
    {
      field: 'authorizeDeviceId',
      label: '门',
      component: 'Select',
      options: doorOptions.value,
      rules: { required: true, message: '门为必填项', trigger: 'change' }
    },
    {
      field: 'avatar',
      label: '照片',
      slot: 'avatar',

      // onChange({ file }) {
      //   console.log('🚀 ~ onChange ~ uploadFile ====> ', file)
      //   if (!file) return
      //   avatar.value = URL.createObjectURL(file.raw as File)
      // },
      componentProps: {
        limit: 1,
        accept: 'image/*'
        // onRemove() {
        //   avatar.value = ''
        // }
      },
      colProps: {
        span: 24
      },
      rules: {
        required: true,
        validator: (_rule, _value, callback) => {
          console.log(avatar.value)
          if (!avatar.value) {
            return callback(new Error('头像为必传项！'))
          }
          callback()
        },
        trigger: 'change'
      }
    },
    {
      field: 'carType',
      component: 'RadioGroup',
      hidden: !isDrive.value,
      defaultValue: 1,
      label: '车牌类型',
      options: getIntDictOptions(DICT_TYPE.NUMBER_PLATE_TYPE),
      colProps: {
        span: 24
      }
    },
    {
      field: 'carNumber',
      label: '车牌号',
      hidden: !isDrive.value,
      slot: 'car-number',
      placeholder: '请填写车牌号',
      colProps: {
        span: 24
      },
      rules: {
        validator(_rule, _value, callback) {
          let number = ''

          for (const key in inputs) {
            number += inputs[key]
          }

          if (!numberPlateInfo.province) return callback(new Error('请选择车牌所属省份'))

          if (!numberPlateInfo.city) return callback(new Error('请选择车牌所属城市代号'))

          if (
            (formRef.value?.formData.visitorType === 1 && number.length < 5) ||
            (formRef.value?.formData.visitorType === 2 && number.length < 6)
          ) {
            return callback(new Error('车牌号格式不对'))
          }

          callback()
        }
      }
    },
    {
      field: 'reason',
      label: '来访事由',
      component: 'Input',
      placeholder: '请输入...',
      componentProps: {
        type: 'textarea',
        rows: 4
      },
      colProps: {
        span: 24
      },
      rules: { required: true, message: '来访事由为必填项', trigger: 'blur' }
    }
  ]
})

/** 标题 */
const title = computed(() => (isNoAdd.value ? '访客信息' : '创建审批记录'))

const [register, { setModalProps }] = useModalInner((data) => {
  const { action, row } = data as any
  console.log('🚀 ~ const[register]=useModalInner ~ row ====> ', row)

  if (action === 'view' || action === 'edit') {
    status.value = row.status
    recordId.value = row.id
    isNoAdd.value = true
    formRef.value?.disabledFormFields()
    formRef.value?.setFormFieldsValue(row)
    isDrive.value = row.visitorType

    if (row.carNumber) {
      numberPlateInfo.province = row.province
      numberPlateInfo.city = row.carNumber.match(/[A-Z]+/)[0]
      numberPlateInfo.numbers = row.carNumber.match(/[0-9]+/)[0]

      for (let i = 0; i < numberPlateInfo.numbers.length; i++) {
        inputs[`num${i}`] = numberPlateInfo.numbers[i]
      }
    }
    if (row.avatar) {
      const avatarName = row.avatar.match(/(\/\w+)\.jpg|png|jpeg|gif|webp$/)[0]
      avatar.value = new URL(
        `/admin-api/system/user/download${avatarName}?token=${getAccessToken()}&r=${Date.now()}`,
        import.meta.env.VITE_BASE_URL
      ).href
    }
  }

  setModalProps?.({
    footerVisible: !isNoAdd.value || !status.value
  })

  formSchema.formItems![9].hidden = isDrive.value === 2
  formSchema.formItems![10].hidden = !(isDrive.value === 0)
  formSchema.formItems![11].hidden = !(isDrive.value === 1)
  formSchema.formItems![12].hidden = !(isDrive.value === 1)
})

watch(
  isDrive,
  async (newVal) => {
    const res = await getFaceOrCarDataList({ pageNo: 1, pageSize: 100, deviceType: [newVal.value + 4], onLine: 1 })

    formSchema.formItems![9].options = res.list?.map((item) => ({
      label: item.installationPosition,
      value: item.id
    }))
  },
  {
    immediate: true
  }
)

/* 车牌号码信息 */
const numberPlateInfo = reactive({
  province: '',
  city: '',
  numbers: ''
})

/* 车牌号码输入框 */
let inputs = reactive<Record<string, any>>({
  num0: '',
  num1: '',
  num2: '',
  num3: '',
  num4: '',
  num5: ''
})

/** 表单头像上传值变化 */
const handleFileChange = (file, formData, field) => {
  if (!file) return
  formData[field] = file.raw
  avatar.value = URL.createObjectURL(file.raw as File)
}

/** 表单头像上传删除头像 */
const handleRemoveFile = (formData, field) => {
  formData[field] = null
  avatar.value = ''
}

/** 关闭对话框 */
const handleClose = () => {
  isNoAdd.value = false
  if (!isNoAdd.value) {
    URL.revokeObjectURL(avatar.value)
  }
  avatar.value = ''
  formRef.value?.resetFields()
  isDrive.value = 0
  numberPlateInfo.province = ''
  numberPlateInfo.city = ''
  numberPlateInfo.numbers = ''
  Object.keys(inputs).forEach((key) => {
    inputs[key] = ''
  })
}

/** 拒绝 */
const handleCancel = () => {
  setModalProps?.({
    cancelLoading: true
  })
  rejectApprovalAPI(recordId.value)
    .then(() => {
      message.success('操作成功')
      emit('reject')
    })
    .catch((error) => {
      console.log(error)
    })
    .finally(() => {
      setModalProps?.({
        cancelLoading: false
      })
    })
}

/* 提交表单 */
const handleConfirm = async () => {
  console.log(setModalProps)
  setModalProps?.({
    confirmLoading: true
  })
  try {
    if (isNoAdd.value) {
      // confirmLoading.value = true
      await agreeApprovalAPI(recordId.value)
    } else {
      const { isValid, formData } = (await formRef.value?.customValidate()) as {
        isValid: boolean
        formData: IApprovalRecord
      }
      if (!isValid) return

      setModalProps?.({
        confirmLoading: true
      })

      const numbers = Object.values(inputs).join('')
      console.log('🚀 ~ handleConfirm ~ numbers ====> ', numbers)
      const numberPlate = `${numberPlateInfo.city}${numbers}`

      const formdata = new FormData()

      for (const key in formData) {
        formdata.append(key, formData[key])
      }
      formdata.append('carNumber', numberPlate)
      formdata.append('province', numberPlateInfo.province)

      await createApprovalRecordAPI(formdata)
    }

    message.success('操作成功')

    emit('submit:success')
  } catch (error) {
    console.log(error)
  } finally {
    setModalProps?.({
      confirmLoading: false
    })
  }
}
</script>

<style lang="scss" scoped>
.edit-approval {
  .car-province {
    --el-select-width: 70px;

    min-width: 70px !important;
  }

  .car-province {
    & + .car-city {
      --el-select-width: 60px;

      min-width: 60px !important;
      margin-left: 10px;
    }
  }

  .num-input {
    --el-input-width: 40px;

    min-width: 40px !important;
    margin-left: 10px;

    &:deep(.el-input__inner) {
      text-align: center;
    }
  }

  .avatar {
    width: 148px;
    height: 148px;
    object-fit: contain;
  }

  .avatar-uploader {
    &:deep(.el-upload) {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      transition: var(--el-transition-duration-fast);
    }

    &:deep(.el-upload):hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      width: 178px;
      height: 178px;
      font-size: 28px;
      color: #8c939d;
      text-align: center;
    }
  }
}
</style>

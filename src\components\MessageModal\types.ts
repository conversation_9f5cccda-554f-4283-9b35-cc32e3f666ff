/** setModalProps 方法的参数来行 */
export interface IModalProps {
  visible?: boolean
}

/** modal对话框的方法集合类型 */
export interface IModalMethods {
  setModalProps: (props?: IModalProps) => void
  emitVisible?: (visible: boolean, uid: number) => void
}

/** 消息推送对话框的配置项 */
export interface IModalOptions {
  flush?: 'pre' | 'post'
}

/** register函数的类型 */
export type RegisterFn = (modalMethods: IModalMethods, uid: number) => void

/** 返回的方法 */
export interface IMethods {
  openMessageModal: (callback?: () => void, options?: IModalOptions) => void
  closeMessageModal: () => void
}

/** useMessageModalInner 函数的返回值类型 */
export type UseModalInnerReturnType = [RegisterFn, IMethods]

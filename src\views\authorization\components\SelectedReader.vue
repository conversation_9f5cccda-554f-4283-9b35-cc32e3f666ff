<template>
  <div class="selected-reader">
    <h3 class="title">{{ title }}</h3>
    <BasicTable ref="tableRef" :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import type { ITableSchema } from '@/components/BasicTable/types'

import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import type { IReader } from '../types'

defineOptions({
  name: 'SelectedReader'
})

/* ========== props =========== */

const props = defineProps<Partial<ITableSchema>>()

/* ========== emit ========== */

const emit = defineEmits<{
  (e: 'copy-auth'): void
  (e: 'selection-change', selected: any[]): void
}>()

const tableRef = ref<IBasicTableExpose>()

const selectedRollback = ref<any[]>([])

/* 表格配置 */
const tableConfig: ITableSchema = {
  columns: props.columns || [],
  tableCardStyle: {
    body: {
      padding: '0'
    }
  },
  align: 'center',
  enableSearch: true,
  allowSelection: true,
  selectionChange(selected) {
    selectedRollback.value = selected
    emit('selection-change', selected)
  },
  searchFormSchema: {
    formItems: [
      {
        field: 'copy',
        component: 'Button',
        disabled: () => selectedRollback.value.length === 0,
        componentProps: {
          content: '复制权限',
          type: 'primary',
          icon: 'ep:copy-document',
          onClick() {
            emit('copy-auth')
          }
        }
      }
    ],
    hiddenReset: true,
    hiddenSearch: true
  },
  checkboxProps: {
    reserveSelection: true
  },
  paginationProps: {
    pageSize: 15,
    pagerCount: 5,
    layout: 'total, prev, pager, next'
  },
  ortherHeight() {
    const titleHeight = 42
    const searchHeight = 69
    const cardPadding = 20
    return titleHeight + searchHeight + cardPadding
  }
}

/* ========== watch ========== */
let selectedReader: IReader[] = []

watch(
  () => props.data,
  (newData: IReader[]) => {
    selectedReader = newData.reduce<IReader[]>((arr, current) => {
      console.log('current', current)
      const exist = arr.find((item) => item.id === current.id || item.authorizedId === current.id)
      if (!exist) {
        return arr.concat([current])
      } else {
        return arr
      }
    }, [])
    tableConfig.data = selectedReader
    tableRef.value?.getList()
    checkSelectedReader()
  }
)

/** 勾选已选中读卡器 */
const checkSelectedReader = () => {
  nextTick(() => {
    const tableData = unref(tableConfig.data)
    tableData?.forEach((row) => {
      tableRef.value?.toggleRowSelection(row, true)
    })
  })
}

/* 回退读卡器 */
const rollbackReader = () => {
  const tableData = unref(tableConfig.data)
  /* 更新选中状态 */
  if (tableData?.length) {
    for (const row of selectedRollback.value) {
      tableRef.value?.toggleRowSelection(row, false)
    }
  } else {
    tableRef.value?.clearSelection()
  }

  tableConfig.data = tableData?.filter((item) => !selectedRollback.value.some((selected) => selected.id === item.id))

  tableRef.value?.getList()
}

defineExpose({
  rollbackReader
})
</script>

<style lang="scss" scoped>
.selected-reader {
  height: 100%;

  .title {
    margin-bottom: 10px;
    font-size: 22px;
    font-weight: 400;
    color: #409eff;
  }

  &:deep(.el-pagination) {
    padding-right: 15px;
  }

  .basic-table {
    height: 100%;

    &:deep(.table-search) {
      .table-search__body {
        padding: 10px !important;
      }

      .el-form {
        height: auto;

        .el-form-item {
          margin-right: 8px;
          margin-bottom: 0;
        }
      }
    }

    &:deep(.el-card.table-area) {
      height: calc(100% - 110px);

      .el-card__body {
        height: 100%;

        .el-table {
          display: flex;

          &__inner-wrapper {
            width: 100%;

            &::before {
              width: 0;
            }
          }
        }
      }
    }
  }
}
</style>

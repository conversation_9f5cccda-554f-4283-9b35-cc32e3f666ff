<template>
  <div class="permission-card">
    <BasicTable ref="basicTableRef" :table-config="tableConfig" />

    <!-- 切换卡片确认框 -->
    <ChangeCardBox ref="changeCardBoxRef" @update:card="basicTableRef?.refresh" />
    <!-- 销卡确认框 -->
    <LogoutCardBox ref="logoutCardBoxRef" @logout:card="basicTableRef?.refresh" />
    <!-- 冻结解冻卡片确认框 -->
    <FreezeBox
      ref="freezeBoxRef"
      :api-fn="freezeCardAPI"
      status-field="status"
      :row-field="'cardNumber'"
      @freezed="basicTableRef?.refresh"
    />
    <!-- 新增卡片对话框 -->
    <EditCardDialog ref="editCardDialogRef" @submit:success="basicTableRef?.refresh" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import { ITableSchema } from '@/components/BasicTable/types'
import { cardManageListAPI } from '@/api/permission'
import * as DeptApi from '@/api/system/dept'
import { getDictOptions, DICT_TYPE, getDictObj } from '@/utils/dict'
import ChangeCardBox from '../components/ChangeCardBox.vue'
import LogoutCardBox from '../components/LogoutCardBox.vue'
import FreezeBox from '../components/FreezeBox.vue'
import { useRoute } from 'vue-router'
import { freezeCardAPI } from '@/api/permission'
import EditCardDialog from './components/EditCardDialog.vue'

defineOptions({
  name: 'PermissionCard'
})

/* 路由信息对象 */
const route = useRoute()

/* 授权类型 */
const type = (route.meta.query as { type: string })?.type

/* 表格组件实例 */
const basicTableRef = ref<IBasicTableExpose>()

/* 切换卡片box组件实例 */
const changeCardBoxRef = ref<InstanceType<typeof ChangeCardBox>>()

/* 销毁卡片box组件实例 */
const logoutCardBoxRef = ref<InstanceType<typeof LogoutCardBox>>()

/* 新增卡片对话框组件实例 */
const editCardDialogRef = ref<InstanceType<typeof EditCardDialog>>()

/* 冻结/解冻卡片box组件实例 */
const freezeBoxRef = ref<InstanceType<typeof FreezeBox>>()

/* 表格配置 */
const tableConfig: ITableSchema = {
  columns: [
    {
      label: '姓名',
      prop: 'username',
      enableSearch: true,
      searchFormItemProps: {
        component: 'Input'
      }
    },
    {
      label: '部门',
      prop: 'deptName',
      enableSearch: true,
      searchFormItemProps: {
        component: 'Cascader',
        options: DeptApi.getSimpleDeptList.bind(null, {
          systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
        }),
        componentProps: {
          showAllLevels: false,
          props: {
            emitPath: false
          }
        }
      }
    },
    {
      label: '卡号',
      prop: 'cardNumber'
    },
    {
      label: 'IC卡状态',
      prop: 'status',
      dictTag: true,
      dictType: DICT_TYPE.CARD_STATUS,
      dictValueField: 'status',
      enableSearch: true,
      searchFormItemProps: {
        component: 'Select',
        options: getDictOptions('card_status')
      }
    }
  ],
  apiFn: cardManageListAPI,

  toolbar: [
    {
      text: '新增',
      name: 'add',
      icon: 'ep:plus',
      type: 'primary',
      onClick() {
        console.log('新增')
        editCardDialogRef.value?.open()
      }
    }
  ],
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text: '换卡',
        name: 'changCard',
        permission: ['permission:card:change'],
        onClick({ row }) {
          console.log(row)
          changeCardBoxRef.value?.open(row)
        }
      },
      {
        text({ row }) {
          if (row.status === 1) {
            return '冻结'
          } else if (row.status === 2) {
            return '解冻'
          } else {
            return false
          }
        },
        textIndexField: 'status',
        name: 'freezeCard',
        permission: ['permission:card:freeze'],
        type({ row }) {
          if (row.status === 1) {
            return 'warning'
          } else if (row.status === 2) {
            return 'success'
          } else {
            return 'info'
          }
        },
        onClick({ row }) {
          console.log(row)
          freezeBoxRef.value?.openConfirmBox(row)
        },
        disabled({ row }) {
          return row.status === 0
        }
      },
      {
        text: '销卡',
        name: 'logoutCard',
        type: 'danger',
        permission: ['permission:card:logout'],
        onClick({ row }) {
          console.log(row)
          logoutCardBoxRef.value?.openConfirmBox(row)
        }
      }
    ]
  }
}
</script>

<style lang="scss" scoped></style>

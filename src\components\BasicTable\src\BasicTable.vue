<script lang="ts">
import { PropType, getCurrentInstance, withDirectives, watch, unref } from 'vue'
import type { IButtonBtnSchema, ITableSchema } from '../types'
import { ElButton, ElTable, TableInstance, vLoading } from 'element-plus'
import Pagination from '../../Pagination/index.vue'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import TableHeader from './components/TableHeader.vue'
import { type IBasicFormExpose } from '@/components/BasicForm'
import { useResizeObserver } from '@/hooks/web/useResizeObserver'
import { SelectionColumn } from './components/Column/Selection'
import TableToolCom from './components/TableTool'
import OperationColumn from './components/Column/operation'
import ContentColumnsCom from './components/Column/content'

export default defineComponent({
  name: 'BasicTable',
  components: {
    Icon,
    ElTable,
    ElButton,
    Pagination,
    ContentWrap,
    TableHeader,
    SelectionColumn,
    TableToolCom
  },
  props: {
    tableConfig: {
      type: Object as PropType<ITableSchema>,
      required: true
    } // 表格配置
  },

  emits: ['search', 'reset'],

  setup(props, { slots, emit }) {
    const currentInstance = getCurrentInstance()

    const { size } = useResizeObserver()

    const tableConfig = props.tableConfig

    const tableSearchRef = ref<IBasicFormExpose>()

    const paginationRef = ref<InstanceType<typeof Pagination>>()
    const loading = ref(false) // 列表的加载中
    const total = ref(0) // 列表的总页数
    const list = ref<any[]>([]) // 表的数据
    /* 已选择的表格数据 */
    const selectedTableData = ref<any[]>([])
    /* 展开/折叠 */
    const isExpandAll = ref(false)
    /* 重新渲染表格 */
    const refreshTable = ref(true)
    /* 接口返回的数据 */
    const responseData = ref()
    /* 表格工具栏的高度 */
    const tableToolHeight = ref(0)
    /** 表格搜索区域高度 */
    const tableSearchHeight = ref(0)

    /** 动态添加时不重复的表格数据 */
    let tableData = new Map<string, any>()

    /* 查询参数 */
    const pageInfo = reactive({
      pageNo: 1,
      pageSize: 10
    })

    /* 表格高度 */
    const tableHeightCpt = computed(() => {
      /* 顶部工具栏高度 */
      const toolHeader = document.querySelector('#v-tool-header')
      const toolHeaderRect = toolHeader?.getBoundingClientRect()
      const toolHeaderHeight = toolHeaderRect?.height ?? 0

      /* 标签栏的高度 */
      const tagsView = document.querySelector('#v-tags-view')
      const tagsViewRect = tagsView?.getBoundingClientRect()
      const tagsViewHeight = tagsViewRect?.height ?? 0

      /* 内容区域的上内边距 */
      const sectionPaddingTop = 10
      const sectionBorderWidth = 2

      /* 表格搜索的高度 */
      const tableSearchMargin = 15
      const tableSearchBorder = 2
      const searchHeight = tableSearchHeight.value ? tableSearchMargin + tableSearchHeight.value + tableSearchBorder : 0

      /* 表格所在卡片上下内边距 */
      const tableCardPadding = 20

      /* 底部的高度 */
      const footer = document.querySelector('.v-footer')
      const footerRect = footer?.getBoundingClientRect()
      const footerHeight = footerRect?.height ?? 0

      /* 表格tool工具栏高度 */

      const toolMargin = 10
      const toolHeight = tableToolHeight.value ? tableToolHeight.value + toolMargin : 0

      /* 分页器高度 */
      const paginationHeight = 54

      /* 其他高度 */
      const ortherHeight = tableConfig.ortherHeight
        ? typeof tableConfig.ortherHeight === 'function'
          ? tableConfig.ortherHeight()
          : tableConfig.ortherHeight
        : 0

      /* 设置表格的高度 */
      const excludeTableHeight =
        sectionPaddingTop +
        tableCardPadding +
        paginationHeight +
        ortherHeight +
        toolHeaderHeight +
        tagsViewHeight +
        searchHeight +
        toolHeight +
        footerHeight +
        sectionBorderWidth

      return size.height - excludeTableHeight
    })

    /** 搜索表单字段 */
    const tableSearchFormItemsCpt = computed(() => {
      const needSearchColumns = tableConfig.columns
        .filter((column) => column.enableSearch)
        .map((needSearch) => ({
          field: needSearch.searchFormItemProps?.field ?? needSearch.prop,
          label: needSearch.label,
          ...needSearch.searchFormItemProps
        }))

      return [...needSearchColumns, ...(tableConfig.searchFormSchema?.formItems ?? [])]
    })

    watch(
      () => unref(tableConfig.data),
      () => {
        getList()
      },
      {
        immediate: true
      }
    )

    /* 获取列表数据 */
    async function getList() {
      loading.value = true
      try {
        console.log('tableSearchRef', tableSearchRef.value)
        /* 有传入api接口函数的情况： */
        if (tableConfig.apiFn) {
          /* 请求参数 */
          let params
          /* 调用请求前的钩子函数 */
          const addedParams = (tableConfig.beforeFetch && tableConfig.beforeFetch()) || {}
          /* 额外的请求参数 addedParams */
          params = {
            pageNo: pageInfo.pageNo,
            pageSize: pageInfo.pageSize,
            ...addedParams,
            ...tableSearchRef.value?.formData
          }
          /* 发送请求 */
          try {
            const data = await tableConfig.apiFn(params)
            responseData.value = data
            list.value = data.list ?? []
            total.value = data.total ?? 0
            tableConfig.afterFetch && tableConfig.afterFetch(list, tableConfig?.columns)
          } catch (error) {
            console.log(error)
          }
        } else if (tableConfig.data && Array.isArray(unref(tableConfig.data))) {
          const tableList = unref(unref(tableConfig.data))
          /* 不通过接口请求数据的情况 */
          let start = (pageInfo.pageNo - 1) * pageInfo.pageSize
          let end = pageInfo.pageNo * pageInfo.pageSize
          const result: any[] = tableList.slice(start, end)
          list.value = result
          total.value = +tableList.length
        }
        /* 获取表格数据 */
        list.value && setTableDataSource(list.value)
      } finally {
        loading.value = false
      }
    }

    /* 表格选择事件处理函数 */
    const handleSelectionChange = (newSelection: any[]) => {
      selectedTableData.value = newSelection
      tableConfig.selectionChange?.(newSelection)
    }

    function setTableDataSource(data: any[]) {
      if (!Array.isArray(data)) return
      data.forEach((item) => {
        if (!tableData.has(item.id)) {
          tableData.set(item.id, item)
        }
      })
    }

    /** 获取整个响应数据，不止是表格数据 */
    const getResponseData = () => {
      return responseData.value
    }

    const getTableDataSource = () => {
      return tableData
    }

    /** 树形表格，展开/折叠操作 */
    const toggleExpandAll = () => {
      refreshTable.value = false
      isExpandAll.value = !isExpandAll.value
      nextTick(() => {
        refreshTable.value = true
      })
    }

    /** 重置表格数据 */
    const handleSearch = async () => {
      console.log('formData', tableSearchRef.value?.formData)
      pageInfo.pageNo = tableConfig.paginationProps?.pageNo ?? 1
      pageInfo.pageSize = tableConfig.paginationProps?.pageSize ?? 10
      if (tableConfig.apiFn && typeof tableConfig.apiFn === 'function') {
        await getList()
      }
      emit('search', tableSearchRef.value?.formData)
    }

    /**
     * 重置搜索框数据
     */
    const handleReset = () => {
      handleSearch()
      emit('reset', tableSearchRef.value?.formData)
    }

    /* 刷新表格数据 */
    const refresh = () => handleSearch()

    /* 表格头部工具栏按钮点击事件 */
    function handleClick(button: IButtonBtnSchema) {
      if (button.onClick && typeof button.onClick === 'function') {
        button.onClick(tableSearchRef.value?.formData, getList)
      }
    }

    /* 更新表格指定行和列的值 */
    const updateTableDataByKey = (rowKey: string, columnKey: string, newValue: any) => {
      const row = tableData.get(rowKey)
      if (!row) return
      row[columnKey] = newValue
    }

    onMounted(async () => {
      nextTick(() => {
        tableSearchHeight.value = document.querySelector('.table-search')?.clientHeight ?? 0
        tableToolHeight.value = document.querySelector('.table-tool')?.clientHeight ?? 0
      })
      // await getList()
    })

    /** 向外暴露属性/方法 */
    function changeRef(tableInstance: TableInstance) {
      if (!currentInstance || !tableInstance) return

      const expose = {
        total,
        getList,
        refresh,
        toggleExpandAll,
        getResponseData,
        getTableDataSource,
        updateTableDataByKey
      }

      /** tableInstance解构（遍历属性），Vue会报警告，所以采取下面方式： */
      currentInstance.exposeProxy = currentInstance.exposed = tableInstance

      Object.keys(expose).forEach((key) => {
        if (currentInstance.exposed && currentInstance.exposeProxy) {
          currentInstance.exposeProxy[key] = currentInstance!.exposed[key] = expose[key]
        }
      })
    }

    /** 视图层 */

    function tableHeaderRender() {
      return (tableConfig.enableSearch ??= true)
        ? h(TableHeader, {
            ref: tableSearchRef,
            formSchema: tableConfig.searchFormSchema,
            searchFormItems: tableSearchFormItemsCpt.value,
            cardStyle: tableConfig.searchCardStyle,
            onSearch: handleSearch,
            onReset: handleReset
          })
        : null
    }

    /** 表格 */

    /** 表格列表工具栏 */
    function tableToolRender() {
      return h(TableToolCom, { buttons: tableConfig.toolbar, onClick: handleClick })
    }

    /** 列表 */
    function tableListRender() {
      return withDirectives(
        h(
          ElTable,
          {
            'v-loading': loading.value,
            ref: changeRef,
            data: list.value,
            rowKey: tableConfig.rowKey ?? 'id',
            class: 'talbe',
            height: tableHeightCpt.value,
            maxHeight: tableHeightCpt.value,
            defaultExpandAll: isExpandAll.value,
            headerCellStyle: { backgroundColor: 'var(--app-content-bg-color)' },
            onSelectionChange: handleSelectionChange
          },
          () => [
            h(SelectionColumn, { allowSelection: tableConfig.allowSelection }),
            tableConfig.columns.map((column) =>
              h(
                ContentColumnsCom,
                { column, tableData: list.value },
                {
                  [column.slot as string]: slots[column.slot as string]
                }
              )
            ),
            h(OperationColumn, {
              showActions: tableConfig.showActions,
              actionsColumn: tableConfig.actionsColumn
            })
          ]
        ),
        [[vLoading, loading.value]]
      )
    }

    /** 分页器 */
    function paginationRender() {
      return h(
        'div',
        { class: 'pagination' },
        h(Pagination, {
          ref: paginationRef,
          limit: pageInfo.pageSize,
          page: pageInfo.pageNo,
          'onUpdate:limit': (value) => {
            pageInfo.pageSize = value
          },
          'onUpdate:page': (value) => {
            pageInfo.pageNo = value
          },
          total: total.value,
          pagerCount: tableConfig.paginationProps?.pagerCount,
          layout: tableConfig.paginationProps?.layout,
          size: 'small',
          onPagination: getList
        })
      )
    }

    /** 表格 */

    const showToolbar = tableConfig.toolbar && tableConfig.toolbar.length !== 0

    function tableRender() {
      const bodyStyle = tableConfig.tableCardStyle?.body || { padding: '10px' }
      const noBorder = tableConfig.tableCardStyle?.showBorder === false ? 'not-border' : null
      return h(ContentWrap, { class: ['table-area', noBorder], bodyStyle }, () => [
        showToolbar && tableToolRender(),
        tableListRender(),
        paginationRender()
      ])
    }

    return function render() {
      return h('div', { class: 'basic-table' }, [tableHeaderRender(), tableRender()])
    }
  }
})
</script>

<style lang="scss" scoped>
.not-border {
  border: 0;
}

.basic-table {
  width: 100%;

  .table {
    &-search {
      &:deep(.table-search__body) {
        position: relative;
      }

      &:deep(.table-search__body) {
        padding: 18px 10px 0 !important;
      }

      &__btn-group {
        .expan-arrow {
          margin-left: 10px;
          cursor: pointer;
        }
      }
    }

    &-area {
      width: 100%;
      margin-bottom: 0;

      &:deep(.table-action) {
        .cell {
          display: flex;
          align-items: center;
        }

        .more {
          margin-left: 12px;

          &:deep(.el-dropdown-menu) {
            z-index: 99;
          }
        }
      }
    }

    &-tool {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }

    .el-dropdown-link {
      display: flex;
      color: var(--el-color-primary);
      cursor: pointer;
      align-items: center;
    }
  }

  .pagination {
    position: relative;
    height: 54px;
    padding-right: 15px;
    overflow: hidden;

    &:deep(.el-input),
    &:deep(.el-select) {
      min-width: auto;
    }
  }
}
</style>

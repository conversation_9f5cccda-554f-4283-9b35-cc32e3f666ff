/**
 * @description 监视指定元素的大小变化
 */
export const useResizeObserver = () => {

  /** 监视的目标元素 */
  const target = ref<Nullable<Element>>(null)
  /** 目标元素的大小信息 */
  const size = reactive({
    width: 0,
    height: 0
  })

  if (!target.value) {
    target.value = document.body
  }

  /** 创建监视器 */
  const observer = new ResizeObserver((entries) => {
    for (const item of entries) {
      const contentBoxSize = Array.isArray(item.contentBoxSize) ? item.contentBoxSize[0] : item.contentBoxSize
      size.width = contentBoxSize.inlineSize
      size.height = contentBoxSize.blockSize
    }
  })

  onMounted(() => {
    target.value && observer.observe(target.value)
  })

  onBeforeUnmount(() => {
    target.value && observer.unobserve(target.value)
  })

  return {
    size,
    target
  }
}

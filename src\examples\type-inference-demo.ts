/**
 * 动态获取函数返回值类型的示例
 */

import { controllerDoorInfoListAPI } from '@/api/real-time-monitoring/door'
import type { ICardListConf, ExtractApiReturnType, ExtractApiListType } from '@/components/CardList/types'

// ========== 方法1: 使用 ReturnType 工具类型 ==========

// 获取函数的返回值类型
type ControllerDoorInfoListAPIReturnType = ReturnType<typeof controllerDoorInfoListAPI>
// 结果: Promise<PageResult<IDoorInfo[]>>

// 获取 Promise 解析后的类型
type ControllerDoorInfoListAPIDataType = Awaited<ReturnType<typeof controllerDoorInfoListAPI>>
// 结果: PageResult<IDoorInfo[]>

// 获取列表数据的类型
type DoorInfoListType = Awaited<ReturnType<typeof controllerDoorInfoListAPI>>['list']
// 结果: IDoorInfo[]

// 获取单个门信息的类型
type DoorInfoType = Awaited<ReturnType<typeof controllerDoorInfoListAPI>>['list'][number]
// 结果: IDoorInfo

// ========== 方法2: 使用自定义工具类型 ==========

// 使用我们定义的工具类型
type APIReturnType = ExtractApiReturnType<typeof controllerDoorInfoListAPI>
// 结果: PageResult<IDoorInfo[]>

type APIListType = ExtractApiListType<typeof controllerDoorInfoListAPI>
// 结果: IDoorInfo[]

// ========== 方法3: 在组件中使用 ==========

// 正确的类型定义方式
const listConfig: ICardListConf<typeof controllerDoorInfoListAPI> = {
  fields: [
    {
      label: '名称',
      prop: 'doorName'
    },
    {
      label: '控制器编号',
      prop: 'controllerNumber'
    }
  ],
  apiFn: controllerDoorInfoListAPI
}

// ========== 方法4: 创建通用的类型推断函数 ==========

/**
 * 创建一个函数来帮助推断API函数的类型
 */
function createApiConfig<T extends (data: any) => Promise<PageResult<any>>>(
  apiFn: T,
  fields: Array<{ label: string; prop: string; dictType?: string }>
): ICardListConf<T> {
  return {
    fields,
    apiFn
  }
}

// 使用示例
const typedListConfig = createApiConfig(controllerDoorInfoListAPI, [
  {
    label: '名称',
    prop: 'doorName'
  },
  {
    label: '控制器编号',
    prop: 'controllerNumber'
  }
])

// ========== 方法5: 在 Vue 组件中的完整示例 ==========

export function useDoorListTypes() {
  // 定义响应式数据时使用正确的类型
  const doorList = ref<ExtractApiListType<typeof controllerDoorInfoListAPI>>([])
  const selectedDoor = ref<ExtractApiListType<typeof controllerDoorInfoListAPI>[number] | null>(null)
  
  // API调用函数
  const fetchDoorList = async (): Promise<ExtractApiReturnType<typeof controllerDoorInfoListAPI>> => {
    const result = await controllerDoorInfoListAPI({ 
      pageNo: 1, 
      pageSize: 10, 
      doorName: 0 
    })
    doorList.value = result.list
    return result
  }
  
  return {
    doorList,
    selectedDoor,
    fetchDoorList
  }
}

// ========== 解释为什么之前推断为 unknown ==========

/*
问题原因：
1. 原来的 apiFnType 类型定义中，泛型参数 T 有默认值 any
2. 在 ICardListConf 接口中使用 apiFn: apiFnType 时没有传递具体的泛型参数
3. 这导致 TypeScript 无法正确推断出具体的函数类型

解决方案：
1. 使用泛型接口 ICardListConf<T>，让调用者传递具体的API函数类型
2. 创建工具类型来提取API函数的返回值类型
3. 使用 ReturnType 和 Awaited 工具类型来获取函数的返回值类型

现在类型推断应该能正常工作了！
*/

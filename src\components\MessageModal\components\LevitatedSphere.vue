<template>
  <div class="levitated-sphere" @click="handleClick">
    <Icon icon="ep:chat-dot-round" :size="32" />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'MessageBoxLevitatedSphere'
})

const emit = defineEmits<{
  (e: 'click'): void
}>()

/** 点击打开消息提示框 */
const handleClick = () => {
  console.log('打开消息提示框')
  emit('click')
}
</script>

<style lang="scss" scoped>
.levitated-sphere {
  position: absolute;
  right: 15px;
  bottom: 15px;
  display: flex;
  width: 58px;
  height: 58px;
  color: var(--el-color-info);
  cursor: pointer;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 0 8px #ccc;
  justify-content: center;
  align-items: center;
  transition: color 0.2s;

  &:hover {
    color: var(--el-color-primary);
  }
}
</style>

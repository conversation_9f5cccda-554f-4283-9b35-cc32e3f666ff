<template>
  <!-- 分配对话框 -->
  <el-dialog
    v-model="dialogVisible"
    :title="authorizeType === 1 ? '卡片授权' : '指纹授权'"
    class="!w-600px"
    @visible-change="resetForm"
    :show-close="false"
  >
    <!-- 卡片信息表单 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <!-- <el-form-item label="系统类型" prop="systemType">
          <el-select
            v-model="formData.systemType"
            placeholder="选择系统类型"
            clearable
            class="!w-250px"
            @change="handleChange"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
      </el-form-item> -->
      <el-form-item label="部门" prop="deptId">
        <el-select
          v-model="formData.deptId"
          placeholder="选择部门"
          clearable
          class="!w-250px"
          :disabled="(authorizeType === 2 && isRecordSuccess) || isRecording || loading"
        >
          <el-option
            v-for="dept in deptList"
            :key="dept.deptId"
            :label="dept.deptName"
            :value="dept.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户" prop="userId">
        <el-select
          v-model="formData.userId"
          filterable
          clearable
          placeholder="请选择用户"
          class="!w-250px"
          @filter-method="handleUserFilter"
          :disabled="(authorizeType === 2 && isRecordSuccess) || isRecording  || loading"
        >
          <el-option
            v-for="user in filteredUsers"
            :key="user.userId"
            :label="user.username"
            :value="user.userId"
          >
            <span style="float: left">{{ user.username }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ user.deptName }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="卡号" prop="cardNumber" v-if="authorizeType === 1">
        <el-input
          v-model="formData.cardNumber"
          placeholder="请输入卡号"
          clearable
          class="!w-250px"
          :disabled="loading"
        />
      </el-form-item>
      <el-form-item label="指纹" prop="fingerprintType" v-if="authorizeType === 2">
        <div class="flex items-center">
          <el-select v-model="formData.fingerprintType" clearable placeholder="请选择指纹"  class="!w-250px" :disabled="isRecordSuccess || isRecording">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.FINGERPRINT_TEMPLATE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <el-button
            type="primary"
            plain
            :loading="isRecording"
            :disabled="isRecording || !formData.userId || formData.fingerprintType===undefined || formData.fingerprintType===null"
            @click="handleRecordFingerprint"
            class="!ml-10px"
          >
            {{ fingerprintBtnText }}
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="有效期" prop="dateRange" class="!w-500px">
        <el-date-picker
          v-model="formData.dateRange"
          clearable
          type="datetimerange"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm:ss"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :disabled-date="disabledDate"
          :disabled="isRecordSuccess || isRecording || loading"
        />
      </el-form-item>
    </el-form>

    <template #footer>     
      <el-button
        type="primary"
        @click="handleSubmit"
        :loading="loading"
        :disabled="authorizeType === 2 && !isRecordSuccess"
      >
        确认
      </el-button>
      <el-button @click="dialogVisible = false" :disabled="loading">取消</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import * as HardwareApi from '@/api/hardware'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

defineOptions({ name: 'HarewareAuthorityAdd' })

const props = defineProps<{
  equipmentId: number
}>()

const message= useMessage() // 消息弹窗
const dialogVisible = ref(false)// 对话框控制
const loading = ref(false) //表单加载控制避免重复提交
const userDataList=ref([])// 用户数据
const authorizeType=ref(0) // 授权类型：1 - 卡片管理；2 - 指纹管理；
const systemType=ref(0) // 系统类型：0 - 门禁系统；1 - 承包商系统；
const faceId=ref(0) //人脸设备记录id

interface FormData {
  // systemType: number
  deptId?: number
  userId?: number
  cardNumber: string
  fingerprintType?:number
  dateRange: string[]
}

// 表单数据及验证
const formRef = ref()
const formData = ref<FormData>({
  // systemType: undefined,
  deptId: undefined,      
  userId: undefined,       
  cardNumber: '',
  fingerprintType:undefined,
  dateRange: []
})

//表单验证规则
const formRules = reactive({
  // systemType: [{ required: true, message: '请选择系统类型', trigger: 'change' }],
  deptId: [{ required: true, message: '请选择部门', trigger: 'change' }],
  userId: [{ required: true, message: '请选择用户', trigger: 'change' }],
  cardNumber: [
    { required: true, message: '请输入卡号', trigger: 'change' },
    { pattern: /^[A-Za-z0-9]+$/, message: '卡号必须由字母和数字组成', trigger: 'change' }
  ],
  fingerprintType: [{ required: true, message: '指纹不能为空', trigger: 'change' }],
  dateRange: [{ required: true, message: '请选择有效期', trigger: 'change' }]
})

const isRecordSuccess = ref(false) // 添加指纹录入成功状态
const isRecording = ref(false) // 指纹录入状态
// 指纹按钮文本计算属性
const fingerprintBtnText = computed(() => {
  if (isRecording.value) {
    return '正在录入...'
  }
  return isRecordSuccess.value ? '指纹录入成功' : '指纹录入'
})
// 指纹录入处理函数
const handleRecordFingerprint = async () => {
  if (!formData.value.userId) {
    message.error('请先选择用户')
    return
  }
  // 指纹类型检查
  if (formData.value.fingerprintType===undefined || formData.value.fingerprintType===null) {
    message.error('请先选择指纹类型')
    return
  }
  try {
    isRecording.value = true
    // 调用指纹录入API
    const response=await HardwareApi.getFingerprint(faceId.value)
    if (response.code === 0) {
      isRecordSuccess.value = true  // 标记录入成功
      message.success('指纹录入成功')
    }
  } catch (error) {
    console.error('指纹录入失败', error)
    message.error('指纹录入失败，请重试')
  } finally {
    isRecording.value = false
  }
}
//参数1授权类型：1-卡片 2-指纹 参数2系统类型 ：0-门禁系统 1-承包商系统 参数3人脸识别设备记录id
const open= (authType:number,sysType:number,id:number)=>{
  faceId.value=id
  systemType.value=sysType
  authorizeType.value=authType
  dialogVisible.value=true
  resetForm()
  cachedUserData=[]
  getList(sysType) //获取门禁系统人员信息列表
}

// 缓存用户数据
let cachedUserData = []
/**获取人员列表信息 */
const getList= async (systemType:number)=>{
  if(cachedUserData.length){
    userDataList.value=cachedUserData
    return
  }
  try{
    const data=await HardwareApi.getUserFaceList(systemType)
    cachedUserData=data.map(item=> ({
      userId:item.id,
      username:item.username,
      deptId:item.deptId,
      deptName:item.deptName,
      // systemType:item.systemType
    }))
    userDataList.value=cachedUserData
  }catch(error){
    console.log('获取人员列表信息失败',error)
  }
}



// const handleChange= (selectSystemType:number)=>{
//   formData.value = {
//     ...formData.value,
//     systemType: selectSystemType,
//     deptId: undefined,
//     userId: undefined,
//   }
//   cachedUserData=[]
//   getList(selectSystemType)
// }

const deptList = computed(() => {
  // if (!formData.value.systemType) return []
  const deptMap = new Map()
  userDataList.value
  // .filter(user => user.systemType === 0)
  .forEach(user => {
    if (!deptMap.has(user.deptId)) {
      deptMap.set(user.deptId, { 
        deptId: user.deptId, 
        deptName: user.deptName 
      })
    }
  })
  return Array.from(deptMap.values())
})

// 搜索条件
const searchQuery = ref('')
// 过滤后的用户列表
const filteredUsers = computed(() => {
  return userDataList.value.filter(user => {
    // const matchSystemType = user.systemType === 0 
    const matchDept = formData.value.deptId 
      ? user.deptId === formData.value.deptId
      : true
    const matchSearch = user.username.includes(searchQuery.value)
    return   matchDept && matchSearch
  })
})

const handleUserFilter = (query: string) => {
  searchQuery.value = query
}

/** 监听部门选择变化 */
watch(()=>formData.value.deptId,(newDeptId)=>{
  if(formData.value.userId){
    // 检查当前选择的用户是否属于新部门
    const user=userDataList.value.find(u=>u.userId === formData.value.userId)
    if(user?.deptId !== newDeptId){
      formData.value.userId = ''
    }
  }
})

/**监听用户选择变化 */
watch(()=>formData.value.userId,(newUserId)=>{
  if(newUserId){
   const user=userDataList.value.find(u=>u.userId ===newUserId)
   if(user){
    formData.value.deptId=user.deptId
   }
  }
})

/** 定义禁用开始日期的逻辑(今日之前的日期不可选) */
const disabledDate = (time:Date) => {
  // 获取今日凌晨的时间戳（忽略时分秒）
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return time.getTime() < today.getTime()
}

// 提交处理
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const handleSubmit = async () => { 
  try {
    loading.value = true
    await formRef.value.validate()
    const data = {
      authorizeId: formData.value.userId,
      authorizedId: props.equipmentId,
      cardNumber: formData.value.cardNumber,
      systemType: systemType.value,
      authorizeType: authorizeType.value,
      fingerprintType: formData.value.fingerprintType,
      type: 0,
      startTime:formData.value.dateRange?.[0] || '',
      endTime:formData.value.dateRange?.[1] || ''
    }
    await HardwareApi.addFaceAuthorization(data)
    message.success('授权成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    console.error('授权失败')
  }finally{
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    // systemType: undefined,
    deptId: undefined,
    userId: undefined,
    cardNumber: '',
    dateRange: [] // 确保重置为数组
  }
  cachedUserData=[]
  searchQuery.value = ''
  isRecordSuccess.value = false  // 重置指纹成功状态
  formRef.value?.resetFields()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗


</script>
<template>
  <!-- 授权管理对话框 -->
  <el-dialog v-model="dialogVisible" :title="queryParams.authorizeType === 1 ? '卡片管理' : '指纹管理' "  class="!w-1200px">
      <!-- 操作按钮组 -->
      <ContentWrap v-if="showContentWrap">
          <el-form
            ref="queryFormRef"
            :inline="true"
            :model="queryParams"
            class="-mb-15px"
            label-width="68px"
          >
          <!-- <el-form-item label="系统类型" prop="systemType">
            <el-select v-model="queryParams.systemType" class="!w-120px"  placeholder="请选择系统类型" @change="handleChange">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
            
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" plain @click="addCardFaceForm" v-if="showAuthButton">
              <Icon class="mr-5px" icon="ep:plus" />
                授权
            </el-button>
            <el-button type="danger" plain @click="deleteCardFace" :disabled="selectedUserIds.length === 0" v-if="showRevokeButton">
              <Icon class="mr-5px" icon="ep:delete" />
                解权
            </el-button>
          </el-form-item>
        </el-form>
    </ContentWrap>
  <!-- 已授权人员数据列表 -->
  <ContentWrap>
    <el-table 
      ref="tableRef" 
      :data="cardList" 
      v-loading="loading" 
      @selection-change="handleSelectionChange" 
      row-key="id" 
      :header-cell-style="{ backgroundColor: '#fafafa' }"
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column align="center" label="用户名" prop="username" />
        <el-table-column align="center" label="部门名称" prop="deptName"/>
        <el-table-column align="center" label="卡号" prop="cardNumber" v-if="queryParams.authorizeType === 1"/>
        <el-table-column align="center" label="指纹" prop="fingerprintType" v-if="queryParams.authorizeType === 2">
          <template #default="scope">
            <dict-tag :type="DICT_TYPE.FINGERPRINT_TEMPLATE" :value="scope.row.fingerprintType" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="开始时间" prop="startTimeFormatted" />
        <el-table-column align="center" label="结束时间" prop="endTimeFormatted" />
    </el-table>      
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>

  <template #footer>
      <el-button type="primary" @click="dialogVisible=false">取 消</el-button>
  </template>
  </el-dialog>

  <!-- 人脸识别机-智能锁：授权（卡片/指纹） -->
  <HarewareAuthorityAddForm ref="addCardForm" :equipment-id="equipmentId" @success="getList"/>

</template>

<script lang="ts" setup>

import { DICT_TYPE} from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import * as HardwareApi from '@/api/hardware'
import HarewareAuthorityAddForm from './HarewareAuthorityAddForm.vue'
import { hasPermission } from '@/directives/permission/hasPermi'
import { ElMessageBox,ElMessage } from 'element-plus'

defineOptions({ name: 'HardwareAssign' })

const { t } = useI18n()
const loading=ref(false) //列表加载中
const dialogVisible=ref(false) //是否展示对话框
const total=ref(0) //列表总页数
const cardList=ref([]) //已授权人脸人员信息列表
const equipmentId=ref(0) //设备id
const selectedUserIds=ref<number[]>([]) //选中已授权人员id列表
const queryFormRef=ref() //操作表单
const deviceType=ref(0) //设备类型
const tableRef = ref() // 添加表格引用
const allSelectedRowIds = ref<Set<number>>(new Set()) //存储所有选中行的ID（用于跨页选中）

interface QueryParams{
  pageNo: number,
  pageSize: number,
  type:number,       //类型：0-授权；1-解权
  systemType?:number, //系统类型：0 - 门禁系统；2 - 承包商系统；
  authorizeType?:number, // 授权类型：1 - 卡片管理；2 - 指纹管理；
  id?:number
}

const queryParams = reactive<QueryParams>({
  pageNo: 1,
  pageSize: 10,
  type:0,
  systemType:undefined,
  authorizeType:undefined,
  id:undefined
})

const permissionMap = {

  0: {     // 门禁系统
    3: {   // 智能锁
      1: { // 卡片管理
        auth: ['smartlock:card:authorization'],
        revoke: ['smartlock:card:revocation']
      },
      2: { // 指纹管理
        auth: ['smartlock:fingerprint:authorization'],
        revoke: ['smartlock:fingerprint:revocation']
      }
    },
    4: {   // 人脸识别机
      1: { // 卡片管理
        auth: ['face:card:authorization'],
        revoke: ['face:card:revocation']
      },
      2: { // 指纹管理
        auth: ['face:fingerprint:authorization'],
        revoke: ['face:fingerprint:revocation']
      }
    }
  },
  
  1: {     // 承包商系统
    3: {   // 智能锁
      1: { // 卡片管理
        auth: ['device:smartlock:card:authorization'],
        revoke: ['device:smartlock:card:revocation']
      },
      2: { // 指纹管理
        auth: ['device:smartlock:fingerprint:authorization'],
        revoke: ['device:smartlock:fingerprint:revocation']
      }
    },
    4: {   // 人脸识别机
      1: { // 卡片管理
        auth: ['device:face:card:authorization'],
        revoke: ['device:face:card:revocation']
      },
      2: { // 指纹管理
        auth: ['device:face:fingerprint:authorization'],
        revoke: ['device:face:fingerprint:revocation']
      }
    }
  }
}


// 计算当前系统类型、设备类型对应的权限
const currentPermissions = computed(() => {
  const systemMap = permissionMap[queryParams.systemType!]
  if (!systemMap) return {}
  
  const deviceMap = systemMap[deviceType.value]
  if (!deviceMap) return {}
  
  return deviceMap[queryParams.authorizeType] || {}
})

// 计算是否显示授权按钮
const showAuthButton = computed(() => {
  const auth = currentPermissions.value.auth;
  return auth != null && !(Array.isArray(auth) && !auth.length) 
    ? hasPermission(auth) 
    : false
})

// 计算是否显示解权按钮
const showRevokeButton = computed(() => {
  const revoke = currentPermissions.value.revoke;
  return revoke != null && !(Array.isArray(revoke) && !revoke.length) 
    ? hasPermission(revoke) 
    : false
})

// 操作栏显示
const showContentWrap = computed(() => {
  return showAuthButton.value || showRevokeButton.value
})

/** 打开卡片管理对话框 */
const open=(authType:number,systemType:number,id:number,decType:number)=>{
  queryParams.pageNo=1
  equipmentId.value=id
  queryParams.systemType=systemType
  queryParams.authorizeType=authType
  deviceType.value=decType
  // 重置选中状态
  allSelectedRowIds.value.clear()
  selectedUserIds.value = []
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
  dialogVisible.value=true
  getList()
}

/** 根据系统类型 获取已授权人员列表*/
// const handleChange= (selectSystemType:number)=>{
//   queryParams.pageNo = 1
//   queryParams.systemType=selectSystemType
//   getList()
// }

/**获取已授权人员列表 */
const getList=async ()=>{
  loading.value = true
  try{
    queryParams.id=equipmentId.value
    const data =await HardwareApi.getFaceAssignDataList(queryParams)
     // 一次性格式化日期 减少重复计算
    cardList.value=data.list.map(item=>({
      ...item,
      startTimeFormatted:formatDate(item.startTime),
      endTimeFormatted:formatDate(item.endTime)
    }))
    total.value = data.total
    // 数据加载完成后恢复选中状态
    nextTick(() => {
      if (tableRef.value) {
        cardList.value.forEach(row => {
          if (allSelectedRowIds.value.has(row.id)) {
            tableRef.value!.toggleRowSelection(row, true)
          }
        })
      }
    })
  }finally{
    loading.value = false
  }
}

/** 新增卡片授权*/
const addCardForm=ref()
const addCardFaceForm= () =>{
  //参数1授权类型：1-卡片 2-指纹 参数2系统类型 ：0-门禁系统 1-承包商系统 参数3人脸识别设备记录id
  addCardForm.value.open(queryParams.authorizeType,queryParams.systemType,equipmentId.value) 
}

/**解除卡片授权 */
const deleteCardFace=async ()=>{
  const oldSelectedUserIds=[...selectedUserIds.value]
  ElMessageBox.confirm('是否对以下用户进行解权？','系统提示',
    {
      showCancelButton: true,
      confirmButtonText: t('common.ok'),
      cancelButtonText: t('common.cancel'),
      type: 'warning',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      beforeClose: async (action, instance, done) => { 
        if  (action === 'confirm') { 
          instance.confirmButtonLoading = true
          instance.confirmButtonText = 'Loading...'
          try{
            const timeout=oldSelectedUserIds.length*18000
            const response=await HardwareApi.deleteFaceAuthorization(oldSelectedUserIds,timeout)
            if(response.code===0){ 
              allSelectedRowIds.value.clear()
              selectedUserIds.value=[]
              ElMessage.success('全部用户解权成功')       
            }else if(response.code===200){ 
              // 部分失败时，保留失败用户的选中状态
              const failedIdsArray  = JSON.parse(response.data)

              //移除已选中用户中成功解权的用户
              oldSelectedUserIds.forEach(id => {
                  if (!failedIdsArray.includes(id)) { 
                    allSelectedRowIds.value.delete(id)
                  }
               })

              // 更新选中行ID列表
              selectedUserIds.value = Array.from(allSelectedRowIds.value)
              ElMessage.warning(`${failedIdsArray.length}名用户解权失败，请尝试重新解权`)
            }
          }catch(error){ 
            instance.confirmButtonLoading = false
            instance.confirmButtonText = t('common.ok')
          }finally{ 
            instance.confirmButtonLoading = false
            instance.confirmButtonText = t('common.ok')
            await getList()
            done()
          }
        }else { 
          //处理取消/关闭操作
          if (instance.confirmButtonLoading) {
              ElMessage({
                type: 'warning',
                message: '任务执行中,禁止关闭/取消'
              })
              //异步执行中 禁止关闭/取消
              return
            }
            done() // 取消操作直接关闭
            ElMessage({
              type: 'info',
              message: '取消'
            })
        }

      }
    }
  )
    
  //   await message.strongConfirm('是否对以下用户进行解权？', '系统提示', async () => {
  //     const timeout=selectedUserIds.value.length*18000
  //     await HardwareApi.deleteFaceAuthorization(selectedUserIds.value,timeout)
  //     // const response=await HardwareApi.deleteFaceAuthorization(selectedUserIds.value,timeout)
  //     // if(response.code==0){
  //     //   message.success('全部用户解权成功')
  //     // }else if(response.code==-1){
  //     //   message.warning('部分用户解权失败,请重新解权')
  //     // }
  //     // message.success('解权成功')
  //     //解权成功 清空选中的id
  //     selectedUserIds.value=[]
  //   })
  // }catch(error){
  //     console.log('解权失败'+error)
  // }finally{ 
  //   getList()
  // }
  
}

/**获取选中已授权人脸人员信息id */
const handleSelectionChange=(selection: any[])=>{
  // 更新当前页行ID
  const currentPageIds = new Set(cardList.value.map(item => item.id))

  //更新当前页选中行ID
  const currentPageSelectedIds = new Set(selection.map(row => row.id))

  // 更新当前页的选中行状态
  currentPageIds.forEach(id => {
    if (currentPageSelectedIds.has(id)) {
      allSelectedRowIds.value.add(id) // 当前页添加选中行的ID
    } else {
      allSelectedRowIds.value.delete(id) // 当前页移除取消选中行的ID
    }
  })

  // 更新选中行的ID
  selectedUserIds.value = Array.from(allSelectedRowIds.value)
  //console.log('选中行ID列表', selectedUserIds.value)
}

defineExpose({open}) //暴露open方法 用于父组件调用


</script>

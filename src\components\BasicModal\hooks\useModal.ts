import { ref, reactive, getCurrentInstance } from 'vue'
import type {
  IModalProps,
  UseModalReturnType,
  IOpenModalInnerOptions,
  ICloseModalOptions,
  IModalMethods,
  IModalOptions,
  CallbackFnType,
  IOpenModalOptions
} from '../types'

/* 所有对话框的可见性 */
const visibleData = reactive<{ [key: number]: boolean }>({})
/* 所有传给对应对话框得数据 */
const dataTranfer = reactive<{ [key: number]: any }>({})

export const useModal = (options?: IModalOptions): UseModalReturnType => {
  /* modal对话框 */
  const modal = ref<Nullable<IModalMethods>>(null)
  /** 对话框组件得id */
  const uuid = ref<number>()

  const register = (modalMethods: IModalMethods, uid: number) => {
    console.log(uid)
    uuid.value = uid
    /* 当对话框可见性变化时，设置所有对话框可见性映射表对应uid的值 */
    modalMethods.emitVisible = (visible: boolean, uid: number) => {
      visibleData[uid] = visible
    }
    modal.value = modalMethods
  }

  /* 获取对话框组件实例 */
  const getInstance = () => {
    const instance = modal.value
    if (!instance) return
    return instance
  }

  const methods = {
    /* 打开对话框 */
    openModal<T = any>(openOptions?: IOpenModalOptions<T>) {
      openOptions?.beforeOpen && openOptions.beforeOpen()
      getInstance()?.setModalProps({
        visible: true
      })
      if (!openOptions?.data) return
      dataTranfer[unref(uuid) as number] = openOptions.data
      openOptions?.afterOpen && openOptions.afterOpen()
    },

    /* 关闭对话框 */
    closeModal(closeOptions?: ICloseModalOptions) {
      closeOptions?.beforeClose && closeOptions.beforeClose()
      getInstance()?.setModalProps({
        visible: false
      })
    },
    /** 设置对话框属性 */
    setModalProps: getInstance()?.setModalProps
  }

  return [register, methods]
}

export const useModalInner = (callbackFn?: CallbackFnType): UseModalReturnType => {
  /** 组件的id */
  const uuid = ref<number>()

  /* 获取当前组件实例 */
  const currentInstance = getCurrentInstance()

  /* 获取modal组件所有属性和方法 */
  const modalMethods = ref<Nullable<IModalMethods>>(null)

  const register = (modalInstance: IModalMethods, uid: number) => {
    console.log(uid)
    uuid.value = uid
    modalMethods.value = modalInstance
    currentInstance?.emit('register', modalInstance, uid)
  }

  const getInstance = () => {
    const instance = modalMethods.value
    if (!instance) return
    return instance
  }

  const methods = {
    /* 打开对话框 */
    openModal(openOptions?: IOpenModalInnerOptions) {
      openOptions?.beforeOpen && openOptions.beforeOpen()
      getInstance()?.setModalProps({ visible: true })
      openOptions?.afterOpen && openOptions.afterOpen()
    },

    /* 关闭对话框 */
    closeModal(closeOptions?: ICloseModalOptions) {
      closeOptions?.beforeClose && closeOptions.beforeClose()
      getInstance()?.setModalProps({ visible: false })
    },
    /** 设置对话框属性 */
    setModalProps: (props: IModalProps) => {
      getInstance()?.setModalProps(props)
    }
  }

  /** 监听数据变化 */
  watchEffect(() => {
    const data = dataTranfer[unref(uuid) as number]
    if (!data) return
    if (!callbackFn || !(typeof callbackFn === 'function')) return
    nextTick(() => {
      callbackFn(data)
    })
  })

  return [register, methods]
}

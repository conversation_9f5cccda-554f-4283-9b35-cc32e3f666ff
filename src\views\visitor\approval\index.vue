<template>
  <div class="visitor-approval">
    <BasicTable ref="tableRef" :table-config="tableConfig">
      <template #car-number="{ row }">
        <span v-if="row.province || row.carNumber">{{ row.province }}·{{ row.carNumber }}</span>
        <span v-else>无</span>
      </template>
    </BasicTable>
    <EditApproval @register="register" @submit:success="handleSubmitSuccess" @reject="handleSubmitSuccess" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'
import { queryApprovalRecordAPI } from '@/api/visitor'
import EditApproval from './components/EditApproval.vue'
import { useModal } from '@/components/BasicModal/hooks/useModal'
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'

defineOptions({
  name: 'VisitorApproval'
})

const router = useRouter()

const [register, { openModal, closeModal }] = useModal()

const message = useMessage()

/** 表格组件实例 */
const tableRef = ref<Nullable<IBasicTableExpose>>(null)

const tableConfig: ITableSchema = {
  beforeFetch() {
    return {
      visitorTypeList: [0, 1, 2]
    }
  },
  apiFn: queryApprovalRecordAPI,
  columns: [
    {
      label: '访客姓名',
      prop: 'visitorName'
    },
    {
      label: '性别',
      prop: 'sex',
      dictTag: true,
      dictType: DICT_TYPE.SYSTEM_USER_SEX
    },
    {
      label: '受访人姓名',
      prop: 'userName',
      enableSearch: true
    },
    {
      label: '开始时间',
      prop: 'startTime',
      width: 180,
      enableSearch: true,
      searchFormItemProps: {
        component: 'DataTimePicker',
        componentProps: {
          type: 'datetime',
          valueFormat: 'YYYY-MM-DD HH:mm:ss'
        }
      }
    },
    {
      label: '结束时间',
      prop: 'endTime',
      width: 180,
      enableSearch: true,
      searchFormItemProps: {
        component: 'DataTimePicker',
        componentProps: {
          type: 'datetime',
          valueFormat: 'YYYY-MM-DD HH:mm:ss'
        }
      }
    },
    {
      label: '授权门名称',
      prop: 'authorizeDeviceName'
    },
    {
      label: '车牌号码',
      prop: 'carNumber',
      slot: 'car-number'
    },
    {
      label: '访问方式',
      prop: 'visitorType',
      dictTag: true,
      dictType: DICT_TYPE.VISITOR_TYPE
    },
    {
      label: '访问原因',
      prop: 'reason'
    },
    {
      label: '访问状态',
      prop: 'status',
      dictTag: true,
      dictType: DICT_TYPE.VISITOR_APPROVAL_STATUS
    }
  ],

  toolbar: [
    {
      name: 'create',
      text: '创建记录',
      icon: 'ep:plus',
      type: 'primary',
      onClick() {
        console.log('创建记录')
        openModal({
          data: {
            action: 'add'
          }
        })
      }
    }
  ],
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text: '详情',
        name: 'detail',
        permission: ['visitor:approval:detail'],
        onClick({ row }) {
          console.log('详情')
          openModal({
            data: {
              action: 'view',
              row
            }
          })
        }
      },
      {
        text: '二维码链接',
        name: 'qrcode',
        permission: ['visitor:approval:qrcode'],
        hidden({ row }) {
          return ![0, 3].includes(+row.status)
        },
        onClick({ row }) {
          console.log('生成二维码')
          router.push({
            path: '/visitor-qrcode',
            query: {
              raw: encodeURI(
                JSON.stringify({
                  id: row.id,
                  hdx: '484458'
                })
              )
            }
          })
        }
      }
    ]
  }
}

/** 提交成功 */
const handleSubmitSuccess = () => {
  closeModal()
  tableRef.value?.refresh()
}

onBeforeRouteLeave(async (to, from) => {
  const url = new URL(to.fullPath, import.meta.url).href
  console.log('🚀 ~ onBeforeRouteLeave ~ url ====> ', url)

  if (to.path === '/visitor-qrcode') {
    try {
      await navigator.clipboard.writeText(url)
      message.success('复制成功!')
    } catch {}
    return false
  }
})
</script>

<style lang="scss" scoped></style>

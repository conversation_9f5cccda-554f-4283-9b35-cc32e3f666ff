import { getCurrentInstance, computed } from 'vue'
import type { IModalMethods, UseModalInnerReturnType, IModalProps, IModalOptions, IMethods } from '../types'

/** modal的uid对应的可见性映射表 */
const VISIBLE_DATA = reactive<{ [key: number]: boolean }>({})

/**
 * @description 仅 MessageModal 一层组件时 或者 MessageModal 被再次封装时，封装的组件中用来注册 MessageModal 使用
 * @returns
 */
export function useMessageModal(): UseModalInnerReturnType {
  /** modal暴露出来的方法 */
  const messageModal = ref<Nullable<IModalMethods>>(null)

  /** MessageModal 组件id */
  const componentId = ref<number>()

  /** 获取当前组件实例 */
  const getInstance = () => {
    const instance = messageModal.value
    if (!instance) return
    return instance
  }

  /** 组件注册 */
  const register = (modalMethods: IModalMethods, uid: number) => {
    /**
     * 将 MessageModal 的可见性暂存到 VISIBLE_DATA 中，以供外层组件获取对话框的可见性
     *
     *  */
    modalMethods.emitVisible = (visible: boolean, uid: number) => {
      VISIBLE_DATA[uid] = visible
    }
    messageModal.value = modalMethods
    componentId.value = uid
  }

  /** 打开消息弹窗 */
  const openMessageModal: IMethods['openMessageModal'] = (callback, options) => {
    const flush = options?.flush ?? 'pre'
    flush === 'pre' && callback?.()
    getInstance()?.setModalProps({
      visible: true
    })
    flush === 'post' && callback?.()
  }

  /** 打开消息弹窗 */
  const closeMessageModal = () => {
    getInstance()?.setModalProps({
      visible: false
    })
  }

  /**
   * 获取 MessageModal 的可见性值
   *
   * ~~：双非按位取反运算符，如果你想使用比Math.floor()更快的方法，那就是它了
   * 对于 负数 向上取整，
   * 对于 正数 向下取整，
   * 对于 非数字 取0。
   *  */
  const getVisible = computed(() => VISIBLE_DATA[~~unref(!componentId)])

  /** 设置消息对话框的属性 */
  const setMessageModalProps = (props: IModalProps) => {
    getInstance()?.setModalProps(props)
  }

  const methods = {
    openMessageModal,
    closeMessageModal,
    setMessageModalProps,
    getVisible
  }

  return [register, methods]
}

/**
 * @description 仅 MessageModal 一层组件时 或者 MessageModal 被再次封装时，封装的组件中用来注册 MessageModal 使用
 * @returns
 */
export function useMessageModalInner(): UseModalInnerReturnType {
  /** modal暴露出来的方法 */
  const messageModal = ref<Nullable<IModalMethods>>(null)

  /** MessageModal 组件id */
  const componentId = ref<number>()

  /** 使用 MessageModal 组件的组件实例 */
  const currentInstance = getCurrentInstance()

  /** 获取当前组件实例 */
  const getInstance = () => {
    const instance = messageModal.value
    if (!instance) return
    return instance
  }

  /** 组件注册 */
  const register = (modalMethods: IModalMethods, uid: number) => {
    console.log(uid)
    messageModal.value = modalMethods
    componentId.value = uid
    currentInstance && currentInstance.emit('register', modalMethods, uid)
  }

  /** 打开消息弹窗 */
  const openMessageModal = () => {
    getInstance()?.setModalProps({
      visible: true
    })
  }

  /** 打开消息弹窗 */
  const closeMessageModal = () => {
    getInstance()?.setModalProps({
      visible: true
    })
  }

  /**
   * 获取 MessageModal 的可见性值
   *
   * ~~：双非按位取反运算符，如果你想使用比Math.floor()更快的方法，那就是它了
   * 对于 负数 向上取整，
   * 对于 正数 向下取整，
   * 对于 非数字 取0。
   *  */
  const getVisible = computed(() => VISIBLE_DATA[~~unref(!componentId)])

  const methods = {
    openMessageModal,
    closeMessageModal,
    getVisible
  }

  return [register, methods]
}

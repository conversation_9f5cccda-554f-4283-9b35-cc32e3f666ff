import request from '@/config/axios'
import type { IAMBTAPIParams, IARBMIParams } from './types'

/**
 * @description 审批事项
 * @param params 请求参数
 * @param params.event 事件名称
 * @param params.approvalStatus 状态（类型）
 * @returns
 */
export function approvalMatterByStatusAPI(params: IAMBTAPIParams) {
  return request.post({
    url: '/door/approvalManagement/page',
    data: params
  })
}

/**
 * @description 通过事项id获取审批记录
 * @param params 请求参数
 * @param params.pid 事项id
 * @param params.systemType 系统类型
 * @returns
 */
export function authorizeRecordByMatterIdAPI(params: IARBMIParams) {
  return request.post({
    url: '/door/allAuthorizeCount/getAuthorizeRecord',
    data: params
  })
}

/**
 * @description 通过事项id同意事项
 * @param id 事项id
 * @returns
 */
export function agreeByMatterIdAPI(id: number) {
  return request.get({
    url: '/door/approvalManagement/agree',
    params: {
      id
    }
  })
}

/**
 * @description 通过事项id驳回事项
 * @param id 事项id
 * @returns
 */
export function disagreeByMatterIdAPI(id: number) {
  return request.get({
    url: '/door/approvalManagement/refuse',
    params: {
      id
    }
  })
}

/**
 * @description 撤销事项
 * @param id 事项id
 * @returns
 */
export function revocationByMatterIdAPI(id: number) {
  return request.get({
    url: '/door/approvalManagement/withdraw',
    params: {
      id
    }
  })
}

/**
 * @description 执行事项
 * @param id 事项id
 * @returns
 */
export function executePassMatterByIdAPI(id: number, systemType: number, total: number) {
  return request.get({
    url: '/door/approvalManagement/execute',
    params: {
      id,
      systemType
    },
    timeout: total * 10000
  })
}

/**
 * @description 更新事项已读状态
 * @param id 事项id
 * @returns
 */
export function updateApprovalStatusAPI(id: number) {
  return request.get({
    url: '/door/approvalManagement/read',
    params: {
      id
    }
  })
}

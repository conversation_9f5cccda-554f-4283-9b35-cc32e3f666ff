import type {
  SelectProps,
  FormItemProps,
  cascaderProps,
  FormRules,
  FormItemRule,
  ButtonProps,
  InputEmits,
  InputProps,
  InputNumberProps,
  TreeComponentProps,
  RadioProps,
  DatePickerProps,
  RadioGroupProps,
  UploadProps
} from 'element-plus'

import { ExtractPropTypes, ExtractPublicPropTypes } from 'vue'

export type FormItemType =
  | FormItem<'Input'>
  | FormItem<'InputNumber'>
  | FormItem<'Select'>
  | FormItem<'TreeSelect'>
  | FormItem<'Cascader'>
  | FormItem<'Button'>
  | FormItem<'Radio'>
  | FormItem<'RadioGroup'>
  | FormItem<'DataTimePicker'>
  | FormItem<'ManualUpload'>

/* 表单结构 */
export interface IFormSchema {
  /* 表单验证规则 */
  rules?: FormRules
  /* 是否行内模式 */
  inline?: boolean
  /* 标签位置 */
  labelPosition?: FormItemProps['labelPosition']
  /* 标签的长度 */
  labelWidth?: string | number
  /* 表单项大小 */
  itemSize?: '' | 'large' | 'default' | 'small'
  /* 表单项结构 */
  formItems?: FormItemType[]
  /** 是否禁用表单 */
  disabled?: boolean
  /** 行配置 */
  rowProps?: { gutter?: number }
  /* 列配置 */
  colProps?: { span?: number | null; offset?: number }
}

/* 表单项基础结构 */
interface BaseFormItem {
  /* 表单字段 */
  field: string
  /* 表单标签 */
  label?: string | ((data?: any, scope?: any) => string)
  /* 规则 */
  rules?: FormItemRule
  /* 提示 */
  placeholder?: string
  /* 是否必填项 */
  required?: boolean
  /* 表单项组件 */
  // component?: ComponentsType
  /* 是否可以一键清空 */
  clearable?: boolean
  /* 禁用 */
  disabled?: boolean | ((formData: any) => boolean)
  /* 标签位置 */
  labelPosition?: FormItemProps['labelPosition']
  /* 标签的长度 */
  labelWidth?: string | number
  /* 表单项大小 */
  size?: FormItemProps['size']
  /* 选择框选项 */
  options?: ICascaderOption[] | IOption[] | ((params?: any) => Promise<any[]>)
  /* el-col的配置 */
  colProps?: {
    span?: number
    offset?: number
    push?: number
    pull?: number
  }
  /** 是否隐藏表单项 */
  hidden?: boolean
  /** 是否隐藏label */
  hiddenLabel?: boolean
  /* 是否插槽 */
  slot?: string
  /* 默认值 */
  defaultValue?: any
  /* change事件 */
  onChange?: (value: any) => void
}

/* 表单项组件类型 */
type ComponentsType =
  | string
  | 'Input'
  | 'InputNumber'
  | 'Select'
  | 'TreeSelect'
  | 'Cascader'
  | 'Button'
  | 'Radio'
  | 'Upload'
  | 'RadioGroup'
  | 'DataTimePicker'

export interface IButtonProps extends Partial<Omit<ButtonProps, 'text'>> {
  content?: string
  icon?: string
  hidden?: boolean | ((params) => boolean)
  iconColor?: string
  onClick?: (...args: any[]) => void
}

// /* Input组件属性 */
interface CustomInputProps extends Partial<InputProps> {
  onInput?: InputEmits['input']
  onClear?: InputEmits['clear']
  onFocus?: InputEmits['focus']
  onBlur?: InputEmits['blur']
}

type CustomInputNumberProps = Partial<InputNumberProps>

// /* Select组件属性 */
interface CustomISelectProps extends Partial<ExtractPropTypes<typeof SelectProps>> {
  /* 该选项是否可以被搜索 */
  filterable?: boolean
  /** 选项标签字段 */
  optionsLabelField?: string
  /* 选项值得字段名 */
  optionsValueField?: any
  onVisibleChange?: (visible: boolean) => void
  onRemoveTag?: (tagValue: any) => void
  onClear?: () => void
  onBlur?: (event: FocusEvent) => void
  onFocus?: (event: FocusEvent) => void
  onPopupScroll?: (data: { scrollTop: number; scrollLeft: number }) => void
}

interface CustomCascaderProps extends ExtractPublicPropTypes<typeof cascaderProps> {}

/* 树下拉选择器特有属性 */
type CustomTreeISelectProps = Partial<TreeComponentProps> & CustomISelectProps

/* 上传图片组件特有属性 */
interface CustomUploadProps extends UploadProps {
  imageUrl?: string | null
}

interface CustomRadioProps extends RadioProps {
  defaultValue?: any
}

interface CustomRadioGroupProps extends RadioGroupProps {
  defaultValue?: any
}

interface ComponentPropsMap {
  Input: CustomInputProps
  InputNumber: CustomInputNumberProps
  Select: CustomISelectProps
  Cascader: CustomCascaderProps
  TreeSelect: CustomTreeISelectProps
  RadioGroup: CustomRadioGroupProps
  Radio: CustomRadioProps
  Button: IButtonProps
  DataTimePicker: Partial<DatePickerProps>
  ManualUpload: Partial<CustomUploadProps>
}

/* 使用条件类型定义表单项组件特有属性 */
type ComponentProps<T extends ComponentsType> = T extends keyof ComponentPropsMap ? ComponentPropsMap[T] : never

type FormItem<T extends ComponentsType = 'Input'> = BaseFormItem & {
  component?: T
  componentProps?: ComponentProps<T>
}

/* select选择器选项结构 */
export interface IOption {
  label: string
  value: any
  [key: string]: any
}

/* 级联选择器选项结构 */
export interface ICascaderOption extends IOption {
  children?: ICascaderOption[]
}

/** 基础表单组件暴露的内容 */
export interface IBasicFormExpose {
  resetFields: () => void
  customValidate: <R extends Record<string, any>>(
    cb?: (isValid: boolean, formData) => void
  ) => Promise<{ isValid: boolean; formData: R }>
  formData: Record<string, any>
  setFormFieldsValue: (data: Record<string, any>) => void
  disabledFormFields: () => void
}

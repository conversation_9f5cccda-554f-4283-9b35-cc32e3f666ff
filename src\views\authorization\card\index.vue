<template>
  <div class="authoriztion">
    <ContentWrap class="flex justify-end">
      <!-- 搜索工作栏 -->

      <el-button type="primary" plain @click="handleAuthOrisation">
        <Icon class="mr-5px" icon="mdi:user-check-outline" />
        授权
      </el-button>
      <el-button v-hasPermi="['system:role:create']" plain type="warning" @click="handleRemoveAuth">
        <Icon class="mr-5px" icon="mdi:user-block-outline" />
        解权
      </el-button>
      <!-- <el-button
            v-hasPermi="['system:role:create']"
            plain
            type="danger"
            @click="handleClearCache"
          >
            <Icon class="mr-5px" icon="carbon:clean" />
            清空缓存
          </el-button> -->
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap
      class="auth-content h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-footer-height)-79px)]"
    >
      <el-row :gutter="10" class="h-full">
        <el-col :span="12">
          <PersonShuttle ref="personShuttleRef" v-bind="psConf" @copy-auth="handleCopyReaderAuth" />
        </el-col>
        <el-col :span="12">
          <ReaderShuttle ref="readerShuttleRef" v-bind="rsConf" @copy-auth="handleCopyPersonAuth" />
        </el-col>
      </el-row>
    </ContentWrap>
    <!-- 复制用户权限对话框 -->
    <CopyUserReader
      ref="copyUserReaderRef"
      :api-fn="cardMandateRecordAPI"
      @submit:selected-reader="handleSubmitSelectedReader"
    />
    <!-- 复制用户权限对话框 -->
    <CopyReaderUser
      ref="copyReaderUserRef"
      :api-fn="userListByCardReaderAPI"
      @submit:selected-user="handleSubmitSelectedUser"
    />
    <!-- 授权对话框 -->
    <Authorisation ref="authorisationRef" />
  </div>
</template>

<script setup lang="ts">
import PersonShuttle from '../components/ShuttleTable/PersonShuttle.vue'
import ReaderShuttle from '../components/ShuttleTable/ReaderShuttle.vue'
import CopyUserReader from '../components/Dialog/CopyUserReader.vue'
import Authorisation from '../components/Dialog/Authorisation.vue'

import type { ITableSchema } from '@/components/BasicTable/types'
import * as DeptApi from '@/api/system/dept'
import {
  readerListAPI,
  alreadyHaveCardPersonsAPI,
  cardMandateRecordAPI,
  userListByCardReaderAPI
} from '@/api/authorization'
import CopyReaderUser from '../components/Dialog/CopyReaderUser.vue'
import { DICT_TYPE, getDictObj } from '@/utils/dict'
import { useShuttle } from '../hooks/useShuttle'

defineOptions({
  name: 'AuthorizationCard'
})

const {
  type,
  authorisationRef,
  personShuttleRef,
  copyReaderUserRef,
  readerShuttleRef,
  copyUserReaderRef,
  handleCopyReaderAuth,
  handleSubmitSelectedReader,
  handleCopyPersonAuth,
  handleSubmitSelectedUser,
  handleAuthOrisation,
  handleRemoveAuth
} = useShuttle()

interface IConfig {
  sourceConfig: Partial<ITableSchema>
  selectedConfig: Partial<ITableSchema>
}

/* ========== 穿梭框配置 ========== */

/* 人员穿梭框 */
const psConf: IConfig = {
  sourceConfig: {
    title: '人员列表',
    apiFn: alreadyHaveCardPersonsAPI,
    columns: [
      {
        label: '用户名',
        prop: 'username',
        fixed: true,
        width: 120,
        enableSearch: true,
        searchFormItemProps: {
          hiddenLabel: true
        }
      },
      {
        label: '部门',
        prop: 'deptName',
        width: 120,
        enableSearch: true,
        searchFormItemProps: {
          field: 'deptId',
          component: 'Cascader',
          hiddenLabel: true,
          componentProps: {
            showAllLevels: false,
            props: {
              emitPath: false
            }
          },
          options: DeptApi.getSimpleDeptList.bind(null, {
            systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
          }),
          onChange(value) {
            console.log(value)
          }
        }
      },
      {
        label: 'IC卡',
        prop: 'cardNumber',
        width: 120
      }
    ],
    searchFormSchema: {
      hiddenReset: true
    }
  },
  selectedConfig: {
    title: '已选人员列表',
    columns: [
      {
        label: '用户名',
        prop: 'username',
        fixed: true,
        width: 120
      },
      {
        label: '部门',
        prop: 'deptName',
        width: 120
      },
      {
        label: 'IC卡',
        prop: 'cardNumber',
        width: 120
      }
    ]
  }
}

/* 设备（读卡器...）穿梭框 */
const rsConf: IConfig = {
  sourceConfig: {
    title: '门列表',
    apiFn: readerListAPI,
    beforeFetch() {
      return {
        cardReaderType: +type,
        cardReaderStatus: 2
      }
    },
    columns: [
      {
        label: '设备位置',
        prop: 'deviceLocation',
        enableSearch: true,
        searchFormItemProps: {
          hiddenLabel: true
        }
      },
      {
        label: '安装位置',
        prop: 'installationPosition'
      }
    ]
  },
  selectedConfig: {
    title: '已选门列表',
    columns: [
      {
        label: '门名称',
        prop: 'deviceLocation'
      }
    ]
  }
}
</script>

<style lang="scss" scoped>
.el-card {
  &.mb-15px.auth-content {
    margin-bottom: 0;

    &:deep(.el-card__body) {
      height: 100%;
    }
  }
}
</style>

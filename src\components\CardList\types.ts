interface ICardItemFieldProps {
  label: string,
  prop: string,
  dictType?: string
}

export interface ICardItemField {
  title: ICardItemFieldProps
  desc: ICardItemFieldProps
  status: ICardItemFieldProps
  image?: string | ((params: any) => string)
}

export interface IActionItem{
  name: string
  text: string
  icon?: string
  onClick: (info) => void
}

/** 卡片列表配置 */
export interface ICardListConf {
  cardItemField: ICardItemField
  apiFn?: (...args) => Promise<any>
  actions?: IActionItem[]
}

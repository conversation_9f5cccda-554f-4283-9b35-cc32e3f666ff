import http from '@/config/axios'
import type { IQueryAccessLogAPIParams } from './types'

/**
 * @description 获取出入日志列表
 * @param {Object} data 请求体参数
 * @param {Number} data.pageNo
 * @param {Number} data.pageSize
 * @param {String} data.username
 * @param {Number} data.logType
 * @param {String} data.dateTime
 * @returns Promise
 */
export function queryAccessLogAPI(data: IQueryAccessLogAPIParams) {
  return http.post({
    url: '/visitor/entryAndExitRecords/page',
    data
  })
}

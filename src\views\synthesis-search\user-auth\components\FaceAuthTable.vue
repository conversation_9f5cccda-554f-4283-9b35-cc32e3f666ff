<template>
  <div class="face-auth-table">
    <BasicTable :table-config="tableConfig">
      <template #week-tab="{ row }">
        <WeekTabs v-if="row.weekTab" :week-tabs="row.weekTab" />
      </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts">
import { BasicTable } from '@/components/BasicTable'
import { ITableSchema } from '@/components/BasicTable/types'
import { faceAuthByUserIdAPI } from '@/api/synthesize-search'
import { useRoute } from 'vue-router'
import { DICT_TYPE } from '@/utils/dict'
import WeekTabs from '../../components/WeekTabs/index.vue'

defineOptions({
  name: 'FaceAuthTable'
})

const route = useRoute()

const tableConfig: ITableSchema = {
  columns: [
    {
      label: '用户名',
      prop: 'username'
    },
    {
      label: '部门',
      prop: 'deptName'
    },
    {
      label: '人脸',
      prop: 'authorizeName'
    },
    {
      label: '安装位置',
      prop: 'installationPosition',
      enableSearch: true
    },
    {
      label: '通行时间',
      prop: 'weekTab',
      slot: 'week-tab'
    },
    {
      label: '状态',
      prop: 'status',
      dictTag: true,
      dictType: DICT_TYPE.FACE_STATUS
    },
    {
      label: '生效时间',
      prop: 'startTime'
    },
    {
      label: '失效时间',
      prop: 'endTime'
    }
  ],
  beforeFetch() {
    return {
      authorizeId: route.query.id,
      authorizeType: 4,
      type: 0
    }
  },
  apiFn: faceAuthByUserIdAPI,

  ortherHeight: 72 // el-tabs 的上下内边距 + border
}
</script>

<style lang="scss" scoped></style>

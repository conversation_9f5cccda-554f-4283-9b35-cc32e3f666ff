<template>
  <div class="permission-fingerprint">
    <BasicTable ref="tableRef" :table-config="tableConfig">
      <template #avatar="{ row }">
        <el-avatar shape="square" size="large">
          <img v-show="row.avatar" class="avatar" :src="row.avatar" :alt="row.avatar" />
          <div class="avatar-wrap" @click="handleChangeAvatar(row)">
            <span>修改头像</span>
          </div>
        </el-avatar>
      </template>
    </BasicTable>

    <!-- 删除确认框 -->
    <RemoveBox ref="removeFaceBoxRef" :api-fn="deleteUser" @removed="tableRef?.refresh" />

    <!-- 修改头像确认框 -->
    <AvatarChangeBox ref="avatarChangeBoxRef" @change:avatar="tableRef?.refresh" />

    <!-- 冻结解冻卡片确认框 -->
    <FreezeBox ref="freezeBoxRef" status-field="faceStatus" :api-fn="freezeFaceAPI" @freezed="tableRef?.refresh" />
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { getAccessToken } from '@/utils/auth'
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import { ITableSchema } from '@/components/BasicTable/types'
import * as DeptApi from '@/api/system/dept'
import { faceManageListAPI, freezeFaceAPI } from '@/api/permission'
import { DICT_TYPE, getDictObj } from '@/utils/dict'
import RemoveBox from '../components/RemoveBox.vue'
import AvatarChangeBox from '../components/AvatarChangeBox.vue'
import FreezeBox from '../components/FreezeBox.vue'
import { deleteUser } from '@/api/system/user'

defineOptions({
  name: 'PermissionFingerprint'
})

/* 路由信息对象 */
const route = useRoute()

/* 授权类型 */
const type = (route.meta.query as { type: string })?.type

/* 表格组件实例 */
const tableRef = ref<IBasicTableExpose>()

/* 删除确认框组件实例 */
const removeFaceBoxRef = ref<InstanceType<typeof RemoveBox>>()

/* 冻结/解冻卡片box组件实例 */
const freezeBoxRef = ref<InstanceType<typeof FreezeBox>>()

/* 修改头像消息弹出框 */
const avatarChangeBoxRef = ref<InstanceType<typeof AvatarChangeBox>>()

/* 表格配置项 */
const tableConfig: ITableSchema = {
  columns: [
    {
      label: '姓名',
      prop: 'username',
      enableSearch: true
    },
    {
      label: '部门',
      prop: 'deptName',
      enableSearch: true,
      searchFormItemProps: {
        field: 'deptId',
        component: 'Cascader',
        componentProps: {
          showAllLevels: false,
          props: {
            emitPath: false
          }
        },
        options: DeptApi.getSimpleDeptList.bind(null, {
          systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
        })
      }
    },
    {
      label: '人脸识别',
      prop: 'faceStatus',
      dictTag: true,
      dictType: DICT_TYPE.FACE_STATUS
    },
    {
      label: '头像修改',
      prop: 'avatar',
      slot: 'avatar',
      toolTip: false
    }
  ],
  beforeFetch() {
    return {
      cardReaderType: +type,
      cardReaderStatus: 0,
      deviceType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
    }
  },
  apiFn: faceManageListAPI,
  afterFetch(list) {
    list.value.forEach((row) => {
      const avatar = /[^\/?#&]+(?=\?|#|&|$)/.exec(row.avatar)?.[0]
      row.avatar = new URL(
        `/admin-api/system/user/download/${avatar}?token=${getAccessToken()}&r=${Date.now()}`,
        import.meta.env.VITE_BASE_URL
      ).href
    })
  },
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text({ row }) {
          if (row.faceStatus === 1) {
            return '冻结'
          } else if (row.faceStatus === 2) {
            return '解冻'
          } else {
            return false
          }
        },
        textIndexField: 'faceStatus',
        permission: ['permission:face:freeze'],
        name: 'freezeCar',
        type({ row }) {
          if (row.faceStatus === 1) {
            return 'warning'
          } else if (row.faceStatus === 2) {
            return 'success'
          } else {
            return 'info'
          }
        },
        onClick({ row }) {
          console.log(row)
          freezeBoxRef.value?.openConfirmBox(row)
        },
        disabled({ row }) {
          return row.faceStatus === 0
        }
      },
      {
        text: '删除',
        textIndexField: 'faceStatus',
        name: 'removeFace',
        type: 'danger',
        permission: ['permission:face:delete'],
        onClick({ row }) {
          console.log(row)
          removeFaceBoxRef.value?.openConfirmBox(row)
        },
        disabled({ row }) {
          return +row.faceStatus !== 1
        }
      }
    ]
  }
}

/* 切换头像 */
const handleChangeAvatar = (row) => {
  console.log(row)
  avatarChangeBoxRef.value?.open(row)
}
</script>

<style lang="scss" scoped>
.permission-fingerprint {
  .el-icon {
    &.v-icon {
      cursor: pointer;
    }
  }

  &:deep(.el-table--default) {
    .cell {
      display: flex;
      align-items: center;
    }
  }

  &:deep(.el-avatar) {
    --el-avatar-bg-color: #fff;

    position: relative;

    .avatar {
      object-fit: contain;
      cursor: pointer;

      &-wrap {
        position: absolute;
        display: flex;
        font-size: 12px;
        cursor: pointer;
        background-color: rgb(0 0 0 / 70%);
        transform: translateY(-100%);
        transition: transform 0.3s;
        inset: 0;
        justify-content: center;
        align-items: center;

        &:hover {
          transform: translateY(0);

          span {
            text-decoration: underline;
          }
        }
      }

      &:hover + .avatar-wrap {
        transform: translateY(0);
      }
    }
  }
}
</style>

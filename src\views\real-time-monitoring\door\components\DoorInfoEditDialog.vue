<template>
  <div class="edit-dialog">
    <Dialog :title="title" v-model="dialogVisible" @close="handleClose">
      <BasicForm ref="formRef" :form-schema="formSchema" />
      <template #footer>
        <el-button type="default" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">确认</el-button>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { Dialog } from '@/components/Dialog'
import { BasicForm } from '@/components/BasicForm'
import type { IBasicFormExpose, IFormSchema } from '@/components/BasicForm/types'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { controllerDoorInfoAddAPI, controllerDoorInfoEditAPI } from '@/api/real-time-monitoring/door'

defineOptions({
  name: 'DoorInfoEditDialog'
})

/* emit */
const emit = defineEmits<{
  (e: 'submit:success'): void
}>()

/* 标题 */
const title = ref('')
/* 对话框显示状态 */
const dialogVisible = ref(false)

/* 表单组件实例 */
const formRef = ref<IBasicFormExpose>()

/* 表单验证规则 */
const formRules = {
  doorName: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  // controllerNumber: [{ required: true, message: '请输入控制器编号', trigger: 'blur' }],
  // position: [{ required: true, message: '请输入门下标', trigger: 'blur' }],
  workType: [{ required: true, message: '请选择工作模式', trigger: 'blur' }],
  doorOpeningDelay: [{ required: true, message: '请设置开门延时', trigger: 'blur' }],
  closedDoorDelay: [{ required: true, message: '请设置关门延时', trigger: 'blur' }]
}

/* 表单配置 */
const formSchema: IFormSchema = {
  rules: formRules,
  labelWidth: 110,
  formItems: [
    {
      field: 'doorName',
      label: '名称',
      component: 'Input',
      placeholder: '请输入名称'
    },
    // {
    //   field: 'controllerNumber',
    //   label: '控制器编号',
    //   component: 'Input',
    //   componentProps: {
    //     placeholder: '请输入控制器编号'
    //   }
    // },
    // {
    //   field: 'position',
    //   label: '门下标',
    //   component: 'Select',
    //   componentProps: {
    //     placeholder: '请输入门下标'
    //   },
    //   options: getIntDictOptions(DICT_TYPE.DOOR_POSITION)
    // },
    {
      field: 'workType',
      label: '工作模式',
      component: 'Select',
      placeholder: '请选择工作模式',
      options: getIntDictOptions(DICT_TYPE.DOOR_WORK_TYPE)
    },
    {
      field: 'doorOpeningDelay',
      label: '开门延时(ms)',
      component: 'Input',
      placeholder: '请设置开门延时',
      componentProps: {
        type: 'number'
      }
    },
    {
      field: 'closedDoorDelay',
      label: '关门延时(ms)',
      component: 'Input',
      placeholder: '请设置开门延时',
      componentProps: {
        type: 'number'
      }
    }
    // {
    //   field: 'status',
    //   label: '是否启用',
    //   component: 'Radio',
    //   options: getIntDictOptions(DICT_TYPE.DOOR_STATUS),
    //   defaultValue: 0
    // }
  ]
}

/* 是否显示加载动画 */
const loading = ref(false)

/* 是否更新 */
const isUpdate = ref(false)

/* 表格行数据 */
const rowData = ref()

/* 对话框关闭事件处理函数 */
const handleClose = () => {
  console.log('对话框关闭')
}

/* 打开对话框 */
const open = (params: { title: string; isUpdate?: boolean; row?: any }) => {
  console.log(params)
  title.value = params.title
  isUpdate.value = params.isUpdate ?? false
  rowData.value = params.row
  dialogVisible.value = true

  if (isUpdate.value) {
    nextTick(() => {
      formRef.value?.setFormFieldsValue({
        id: params.row?.id,
        doorName: params.row?.doorName,
        // controllerNumber: params.row?.controllerNumber,
        // position: params.row?.position,
        workType: params.row?.workType,
        doorOpeningDelay: params.row?.doorOpeningDelay,
        closedDoorDelay: params.row?.closedDoorDelay
        // status: params.row?.status
      })
    })
  }
}

/* 提交 */
const handleSubmit = () => {
  console.log('提交')
  formRef.value?.customValidate((isValid, formData) => {
    if (!isValid) return
    loading.value = true
    // const apiFn = isUpdate.value ? controllerDoorInfoEditAPI : controllerDoorInfoAddAPI

    const data = {
      ...rowData.value,
      ...formData
    }

    controllerDoorInfoEditAPI(data)
      .then((res) => {
        console.log(res)
        ElMessage.success('操作成功')
        emit('submit:success')
        dialogVisible.value = false
      })
      .catch((error) => {
        console.log(error)
      })
      .finally(() => {
        loading.value = false
      })
  })
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>

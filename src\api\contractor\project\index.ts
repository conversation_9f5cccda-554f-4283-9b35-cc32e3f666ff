import http from '@/config/axios'
import type { IAddProjectData, IProjectListAPIData, IPersonnelAddData } from './types'

/**
 * @description 新增项目API接口
 * @param {Object} data
 * @param {Number} data.id
 * @param {String} data.projectName
 * @param {String} data.projectId
 * @param {String} data.projectUser
 * @param {String} data.contractorId
 * @param {String} data.contractorUser
 * @param {String} data.startTime
 * @param {String} data.endTime
 * @returns
 */
export function addProjectAPI(data: IAddProjectData) {
  return http.post({
    url: '/contractor/contractorProjectManagement/add',
    data
  })
}

/**
 * @description 编辑项目API接口
 * @param {Object} data
 * @param {Number} data.id
 * @param {String} data.projectName
 * @param {String} data.projectId
 * @param {String} data.projectUser
 * @param {String} data.contractorId
 * @param {String} data.contractorUser
 * @param {String} data.startTime
 * @param {String} data.endTime
 * @returns
 */
export function editProjectAPI(data: IAddProjectData) {
  return http.put({
    url: '/contractor/contractorProjectManagement/edit',
    data
  })
}

/**
 * @description 承包商项目列表分页查询接口
 * @param {Object} data 查询参数
 * @param {Number} data.pageNo 页码
 * @param {Number} data.pageSize 页大小
 * @param {String} data.contractorId 承包商id
 * @returns
 */
export function projectListAPI(data: IProjectListAPIData) {
  return http.post({
    url: '/contractor/contractorProjectManagement/page',
    data
  })
}

/**
 * @description 项目删除接口
 * @param {Number} id 项目id
 * @returns
 */
export function deleteProjectAPI(id: number) {
  return http.delete({
    url: `/contractor/contractorProjectManagement/delete?id=${id}`
  })
}

/**
 * @description 项目详情接口
 * @param id 项目id
 * @returns
 */
export function projectDetailAPI(id: number) {
  return http.get({
    url: '/contractor/contractorProjectManagement/id',
    params: {
      id
    }
  })
}

interface INode {
  id?: number
  projectId: string
  name: string
  remark: string
}

/**
 * @description 项目添加流程接口
 * @param data
 * @returns
 */
export function projectAddProcessAPI(data: INode[]) {
  return http.post({
    url: '/contractor/contractorProjectFlow/add',
    data
  })
}

/**
 * @description 项目编辑流程接口
 * @param data
 * @returns
 */
export function projectEditProcessAPI(data: INode[]) {
  return http.post({
    url: '/contractor/contractorProjectFlow/edit',
    data
  })
}

/**
 * @description 项目删除流程节点接口
 * @param id 节点id
 * @returns
 */
export function projectDeleteProcessNodeAPI(id: number) {
  return http.delete({
    url: '/contractor/contractorProjectFlow/delete',
    params: {
      id
    }
  })
}

/**
 * @description 项目流程节点完成接口
 * @param id 节点id
 */
export function projectProcessNodeCompleteByIdAPI(id: number) {
  return http.get({
    url: '/contractor/contractorProjectFlow/finisFlow',
    params: {
      id
    }
  })
}

/**
 * @description 项目添加人员接口
 * @param data
 * @returns
 */
export function projectAddPersonnelAPI(data: IPersonnelAddData) {
  return http.post({
    url: '/contractor/contractorProjectPersonAndCar',
    data
  })
}

/**
 * @description 项目删除人员接口
 * @param id 人员id
 * @returns
 */
export function projectDeletePersonnelAPI(id: number) {
  return http.delete({
    url: '/contractor/contractorProjectPersonAndCar/delete',
    params: {
      id
    }
  })
}

/**
 * @description 项目冻结/解冻人员接口
 * @param id 人员id
 * @param type 解冻 - 1/冻结 - 2
 * @returns
 */
export function projectFreezePersonnelAPI(id: number, type: 1 | 2) {
  return http.get({
    url: '/contractor/contractorProjectPersonAndCar/freeze',
    params: {
      id,
      type
    }
  })
}

<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" @close="resetForm">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="80px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户昵称" prop="nickname">
            <el-input v-model="formData.nickname" placeholder="请输入用户昵称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="归属部门" prop="deptId">
            <el-tree-select
              v-model="formData.deptId"
              :data="deptList"
              :props="defaultProps"
              check-strictly
              node-key="id"
              placeholder="请选择归属部门"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="formData.mobile" maxlength="11" placeholder="请输入手机号码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" maxlength="50" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item v-if="!formData.id" label="用户名称" prop="username">
            <el-input v-model="formData.username" placeholder="请输入用户名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="!formData.id" label="用户密码" prop="password">
            <el-input v-model="formData.password" placeholder="请输入用户密码" show-password type="password" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="用户性别" prop="sex">
            <el-select v-model="formData.sex" placeholder="请选择">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="岗位" prop="postIds">
            <el-select v-model="formData.postIds" multiple placeholder="请选择">
              <el-option v-for="item in postList" :key="item.id" :label="item.name" :value="item.id!" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="证件类型" prop="idType">
            <el-select v-model="formData.idType" placeholder="请选择">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.ID_CARD)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号码" prop="idCard">
            <el-input v-model="formData.idCard" placeholder="请输入证件号码" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入内容" type="textarea" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item class="avatar" label="头像" prop="avatar">
            <!-- 本地选择 -->
            <el-upload
              class="avatar-uploader"
              action="#"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="handleChangeFile"
              accept="image/*"
            >
              <!-- <img v-if="imageUrl" :src="imageUrl" class="avatar" /> -->
              <el-icon class="avatar-uploader-icon"><Icon icon="ep:picture" color="#a8abb2" :size="120" /></el-icon>
            </el-upload>
            <!-- 拍照 -->
            <div class="avatar-uploader" @click="handleCheckCamera">
              <div class="el-upload">
                <el-icon class="avatar-uploader-icon"
                  ><Icon icon="lucide:camera" color="#a8abb2" :size="120"
                /></el-icon>
              </div>
            </div>

            <!-- 缩略图 -->
            <div class="thumb">
              <div
                class="thumb-wrapper"
                v-if="imgURL"
                @mouseenter="handleMouseEnterThumb"
                @mouseleave="handleMouseLeaveThumb"
              >
                <img class="picture" :src="imgURL" alt="用户头像" />
                <!-- 遮罩 -->
                <transition name="fade">
                  <div class="shade" v-show="showShade">
                    <div class="actions">
                      <el-icon class="avatar-uploader-icon" @click="handlePreviewPicture"
                        ><Icon icon="mynaui:search-plus" color="#ffffff" :size="24"
                      /></el-icon>
                      <el-icon class="avatar-uploader-icon" @click="handleDownloadPicture"
                        ><Icon icon="system-uicons:download" color="#ffffff" :size="24"
                      /></el-icon>
                      <el-icon class="avatar-uploader-icon" @click="handleDeletePicture"
                        ><Icon icon="material-symbols-light:delete-outline-sharp" color="#ffffff" :size="24"
                      /></el-icon>
                    </div>
                  </div>
                </transition>
              </div>
              <el-icon v-else class="avatar-uploader-icon"
                ><Icon icon="clarity:avatar-line" color="#a8abb2" :size="120"
              /></el-icon>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
  <!-- 预览图片对话框 -->
  <Dialog v-model="previewDialogVisible" title="照片预览" :fullscreen="false">
    <div class="preview-wrapper">
      <img :src="imgURL" style="max-width: 100%; object-fit: contain" />
    </div>
  </Dialog>
  <!-- 拍照对话框 -->
  <Photograph v-model:visible="PhotographDialogVisible" :stream="mediaStream" @output-picture="handleOutputPicture" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions, getDictObj } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { defaultProps, handleTree } from '@/utils/tree'
import * as PostApi from '@/api/system/post'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import { ElMessage } from 'element-plus'
import type { FormRules, UploadFile } from 'element-plus'
import { IFormData } from './types'
import Photograph from './Photograph.vue'
import { getAccessToken } from '@/utils/auth'
import { IBasicFormExpose } from '@/components/BasicForm'

defineOptions({ name: 'SystemUserForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const previewDialogVisible = ref(false) // 预览图片的弹窗的是否展示
const PhotographDialogVisible = ref(false) // 拍照的弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref<IFormData>({
  nickname: '',
  deptId: '',
  mobile: '',
  email: '',
  id: '',
  idType: undefined,
  idCard: '',
  username: '',
  password: '',
  sex: '',
  postIds: [],
  remark: '',
  avatar: '',
  status: CommonStatusEnum.ENABLE,
  roleIds: []
})
const formRules = reactive<FormRules>({
  username: [{ required: true, message: '用户名称不能为空', trigger: 'blur' }],
  nickname: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
  password: [{ required: true, message: '用户密码不能为空', trigger: 'blur' }],
  deptId: [{ required: true, message: '部门不能为空', trigger: ['blur', 'change'] }],
  sex: [{ required: true, message: '性别不能为空', trigger: ['blur', 'change'] }],
  postIds: [{ required: true, message: '岗位不能为空', trigger: ['blur', 'change'] }],
  email: [
    {
      type: 'email',
      message: '请输入正确的邮箱地址',
      trigger: ['blur', 'change']
    }
  ],
  mobile: [
    {
      required: true,
      pattern: /^(?:(?:\+|00)86)?1(?:3[\d]|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8[\d]|9[189])\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur'
    }
  ],
  idType: [
    {
      required: true,
      message: '请选择证件类型',
      trigger: 'change'
    }
  ],
  idCard: [
    {
      required: true,
      validator: (_rule, value, callback) => {
        if (!formData.value.idType) {
          const idCardPattern = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/

          if (idCardPattern.test(value)) {
            callback()
          } else {
            callback(new Error('身份证格式不正确'))
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})
const formRef = ref<IBasicFormExpose>() // 表单 Ref
const deptList = ref<Tree[]>([]) // 树形结构
const postList = ref([] as PostApi.PostVO[]) // 岗位列表

/* 头像预览url */
const imgURL = ref('')

/* 是否显示头像遮罩层 */
const showShade = ref(false)

/* 拍照摄像头视频流 */
const mediaStream = ref<MediaStream | null>(null)

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await UserApi.getUser(id)
      if (formData.value.avatar) {
        imgURL.value = new URL(
          `/admin-api/system/user/download/${formData.value.avatar}?token=${getAccessToken()}&r=${Date.now()}`,
          import.meta.env.VITE_BASE_URL
        ).href
      }
    } finally {
      formLoading.value = false
    }
  }
  // 加载部门树
  deptList.value = handleTree(
    await DeptApi.getSimpleDeptList({ systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 1)?.value })
  )
  // 加载岗位列表
  postList.value = await PostApi.getSimplePostList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/* 选择头像 */

/* 鼠标移入头像缩略图事件处理函数 */
const handleMouseEnterThumb = () => {
  /* 显示头像遮罩层 */
  showShade.value = true
}

/* 鼠标移出头像缩略图事件处理函数 */
const handleMouseLeaveThumb = () => {
  /* 隐藏头像遮罩层 */
  showShade.value = false
}

/* 头像文件选择变化触发 */
const handleChangeFile = (uploadFile: UploadFile) => {
  console.log(uploadFile)
  /* 释放之前创建的URL对象 */
  if (imgURL.value) URL.revokeObjectURL(imgURL.value)
  if (uploadFile.raw) {
    /* 保存图片文件 */
    formData.value.avatar = uploadFile.raw
    /* 生成图片临时URL */
    imgURL.value = URL.createObjectURL(uploadFile.raw)
  }
}

/* 拍照 */
const handleCheckCamera = async () => {
  console.log('拍照')
  try {
    PhotographDialogVisible.value = true

    /* 设备（电脑/手机） */
    const devices = window.navigator.mediaDevices
    /* 媒体设备（摄像头、麦克风...） */
    mediaStream.value = await devices.getUserMedia({
      audio: false,
      video: {
        width: 300,
        height: 400,
        facingMode: 'user'
      }
    })
    console.log(mediaStream.value)
  } catch (error: any) {
    switch (error.name) {
      case 'NotFoundError':
        ElMessage.error('请连接摄像头再试！')
        break
      case 'NotAllowedError':
        ElMessage.error('请授权媒体访问！')
        break
      case 'SecurityError':
        ElMessage.error('设备媒体被禁止！')
        break
      case 'NotReadableError':
        ElMessage.error('某个硬件、浏览器或者网页层面发生的错误导致设备无法被访问！')
        break
      default:
        ElMessage.error('发生未知错误！')
    }
    // const errorType = error.split(':')
    // console.log(errorType)
  }
}

/* 获取拍的照片 */
const handleOutputPicture = (picture) => {
  /* 释放之前创建的URL对象 */
  if (imgURL.value) URL.revokeObjectURL(imgURL.value)
  imgURL.value = URL.createObjectURL(picture)
  formData.value.avatar = picture
}

/* 预览 */
const handlePreviewPicture = () => {
  console.log('预览图片')
  previewDialogVisible.value = true
}
/* 下载 */
const handleDownloadPicture = () => {
  if (!imgURL.value || !formData.value.avatar) return
  const link = document.createElement('a')
  console.log(imgURL.value)
  link.download =
    typeof formData.value.avatar === 'string'
      ? formData.value.avatar.match(/[^/]+$/)![0]
      : formData.value.avatar.name
        ? formData.value.avatar.name
        : 'avatar'
  link.href = imgURL.value
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
/* 删除 */
const handleDeletePicture = () => {
  /* 销毁头像url */
  URL.revokeObjectURL(imgURL.value)
  imgURL.value = ''
  /* 重置表单头像字段 */
  formData.value.avatar = null
  /* 关闭摄像头 */
  mediaStream.value?.getVideoTracks().forEach((track) => {
    track.stop()
  })

  mediaStream.value = null
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value?.customValidate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const formdata = new FormData()
    const data = formData.value as unknown as UserApi.UserVO

    for (const key in data) {
      formdata.append(key, data[key])
      if (formType.value !== 'create' && key === 'avatar') {
        if (typeof data[key] === 'string') {
          formdata.delete(key)
        }
      }
    }

    formdata.append('systemType', '1')

    if (formType.value === 'create') {
      await UserApi.createUser(formdata)
      message.success(t('common.createSuccess'))
    } else {
      await UserApi.updateUser(formdata)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value.id = ''
  formData.value.username = ''
  formData.value.password = ''
  formRef.value?.resetFields()
  /* 销毁头像url */
  handleDeletePicture()
}
</script>

<style lang="scss" scoped>
.avatar {
  &:deep(.el-form-item__content) {
    justify-content: space-between;
  }

  &-uploader {
    &:deep(.el-upload) {
      position: relative;
      margin-right: 5px;
      overflow: hidden;
      cursor: pointer;
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);
      }

      .el-icon {
        &.avatar-uploader-icon {
          width: 178px;
          height: 178px;
          font-size: 28px;
          color: #8c939d;
          text-align: center;
        }
      }
    }
  }

  .thumb {
    position: relative;
    width: 178px;
    height: 178px;
    overflow: hidden;
    border: solid 1px var(--el-border-color);
    border-radius: 6px;

    &-wrapper {
      width: 100%;
      height: 100%;
    }

    .el-icon {
      &.avatar-uploader-icon {
        width: 100%;
        height: 100%;
        text-align: center;
      }
    }

    .shade {
      position: absolute;
      display: flex;
      background-color: rgb(0 0 0 / 50%);
      align-items: center;
      inset: 0;

      .actions {
        flex: 1;
        display: flex;
        justify-content: space-evenly;

        .el-icon {
          &.avatar-uploader-icon {
            width: auto;
            height: auto;
            cursor: pointer;
          }
        }
      }
    }

    .picture {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

.preview-wrapper {
  display: flex;
  justify-content: center;
}
</style>

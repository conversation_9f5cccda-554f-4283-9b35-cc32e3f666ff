/* 新增项目请求体参数类型 */
export interface IAddProjectData {
  id?: number
  projectName: string
  projectId: string
  projectUser: string
  contractorId: string
  contractorUser: string
  startTime: string
  endTime: string
}

/* 项目列表分页查询参数类型 */
export interface IProjectListAPIData extends ITableApiParams {
  projectName?: string
  contractorId?: string
  status?: number
}

/* 项目添加人员接口参数类型 */
export interface IPersonnelAddData {
  projectId: number
  userOrCarIdList: number[]
  type: number
}

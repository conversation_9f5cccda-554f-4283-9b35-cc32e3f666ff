<template>
  <div class="permission-fingerprint">
    <BasicTable ref="basicTableRef" :table-config="tableConfig">
      <template #thumb="{ row }">
        <Icon
          icon="material-symbols:fingerprint"
          :size="24"
          :color="row.thumb ? '#F56C6C' : '#909399'"
          data-name="thumb"
          @click="() => handleModifyFingerprint(row, 'thumb')"
        />
      </template>
      <template #indexFinger="{ row }">
        <Icon
          icon="material-symbols:fingerprint"
          :size="24"
          :color="row.indexFinger ? '#F56C6C' : '#909399'"
          @click="() => handleModifyFingerprint(row, 'indexFinger')"
        />
      </template>
      <template #middleFinger="{ row }">
        <Icon
          icon="material-symbols:fingerprint"
          :size="24"
          :color="row.middleFinger ? '#F56C6C' : '#909399'"
          data-name="middleFinger"
          @click="() => handleModifyFingerprint(row, 'middleFinger')"
        />
      </template>
    </BasicTable>

    <!-- 冻结指纹box -->
    <FreezeFingerprintBox ref="freezeFingerprintBoxRef" @freeze:fingerprint="basicTableRef?.refresh" />

    <!-- 新增/修改指纹对话框 -->
    <EditFingerPrintDialog ref="editFingerPrintDialogRef" @edit-fingerprint="basicTableRef?.refresh" />

    <!-- 删除指纹消息盒子 -->
    <DeleteFingerprintBox ref="deleteFingerprintBoxRef" @delete:success="basicTableRef?.refresh" />
  </div>
</template>

<script setup lang="ts">
// import { useRoute } from 'vue-router'
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import { ITableSchema } from '@/components/BasicTable/types'
import * as DeptApi from '@/api/system/dept'
import { getDictOptions, DICT_TYPE, getDictObj } from '@/utils/dict'
import { fingerprintManageListAPI } from '@/api/permission'
import FreezeFingerprintBox from '../components/FreezeFingerprintBox.vue'
import EditFingerPrintDialog from '../components/EditFingerPrintDialog.vue'
import DeleteFingerprintBox from '../components/DeleteFingerprintBox.vue'

defineOptions({
  name: 'PermissionFingerprint'
})

/* 路由信息对象 */
// const route = useRoute()

/* 授权类型 */
// const type = (route.meta.query as { type: string })?.type

/* 表格组件实例 */
const basicTableRef = ref<IBasicTableExpose>()

/* 指纹确认对话框组件实例 */
const freezeFingerprintBoxRef = ref<InstanceType<typeof FreezeFingerprintBox>>()

/* 新增/修改指纹对话框组件实例 */
const editFingerPrintDialogRef = ref<InstanceType<typeof EditFingerPrintDialog>>()

/* 删除指纹消息盒子组件实例 */
const deleteFingerprintBoxRef = ref<InstanceType<typeof DeleteFingerprintBox>>()

/* 表格配置项 */
const tableConfig: ITableSchema = {
  columns: [
    {
      label: '姓名',
      prop: 'username',
      enableSearch: true
    },
    {
      label: '部门',
      prop: 'deptName',
      enableSearch: true,
      searchFormItemProps: {
        component: 'Cascader',
        componentProps: {
          showAllLevels: false,
          props: {
            emitPath: false
          }
        }
      }
    },
    {
      label: '大拇指',
      prop: 'thumb',
      slot: 'thumb'
    },
    {
      label: '食指',
      prop: 'indexFinger',
      slot: 'indexFinger'
    },
    {
      label: '中指',
      prop: 'middleFinger',
      slot: 'middleFinger'
    },
    {
      label: '状态',
      prop: 'status',
      dictTag: true,
      dictType: DICT_TYPE.CARD_STATUS
    }
  ],
  apiFn: fingerprintManageListAPI,
  searchFormSchema: {
    formItems: [
      {
        field: 'type',
        label: '指纹模板',
        component: 'Select',
        options: getDictOptions('fingerprint_template')
      }
    ]
  },
  toolbar: [
    {
      text: '新增',
      name: 'add',
      type: 'primary',
      icon: 'ep:plus',
      onClick: () => {
        editFingerPrintDialogRef.value?.open({ title: '新增指纹' })
      }
    }
  ],
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text({ row }) {
          if (row.status === 1) {
            return '冻结'
          } else if (row.status === 2) {
            return '解冻'
          } else {
            return false
          }
        },
        permission: ['permission:fingerprint:freeze'],
        textIndexField: 'status',
        name: 'freeze',
        type({ row }) {
          if (row.status === 1) {
            return 'warning'
          } else if (row.status === 2) {
            return 'success'
          } else {
            return 'info'
          }
        },
        onClick({ row }) {
          console.log(row)
          freezeFingerprintBoxRef.value?.openConfirmBox(row)
        },
        disabled({ row }) {
          return row.status === 0
        }
      },
      {
        text({ row }) {
          if (row.status === 1) {
            return '删除'
          } else {
            return false
          }
        },
        name: 'delete',
        type: 'danger',
        permission: ['permission:fingerprint:delete'],
        onClick({ row }) {
          console.log(row)
          deleteFingerprintBoxRef.value?.open(row)
        },
        disabled({ row }) {
          return row.status !== 1
        }
      }
    ]
  }
}

/* 修改指纹 */
const handleModifyFingerprint = (row, type) => {
  editFingerPrintDialogRef.value?.open({ isUpdate: true, row, type, title: '修改指纹' })
}
</script>

<style lang="scss" scoped>
.permission-fingerprint {
  .el-icon {
    &.v-icon {
      cursor: pointer;
    }
  }
}
</style>

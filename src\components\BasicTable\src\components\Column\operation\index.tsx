import { <PERSON><PERSON>utton, ElDropdown, ElDropdownItem, ElDropdownMenu, ElTableColumn } from 'element-plus'
import { Icon } from '@/components/Icon'
import { useI18n } from '@/hooks/web/useI18n'
import { operationProps, type OperationProps } from './types'

export default function OperationColumnCom(props: OperationProps) {
  const { t } = useI18n()
  /* 表格操作列按钮加载效果 */
  const actionBtnLoadingMap = ref<Record<string, boolean>>({})

  /* 表格操作列按钮点击事件 */
  const handleActionBtnClick = async (action, scope) => {
    if (!action.enableLoading) return action.onClick?.(scope)
    try {
      actionBtnLoadingMap.value[action.name] = true
      await action.onClick?.(scope)
    } finally {
      actionBtnLoadingMap.value[action.name] = false
    }
  }

  /** 操作列按钮 */
  function operationsBtns(scope) {
    const limit = props.actionsColumn?.limit ?? props.actionsColumn?.actions.length

    return props.actionsColumn?.actions.slice(0, limit).map((action) => {
      const type = action.type ? (typeof action.type === 'function' ? action.type(scope) : action.type) : 'primary'

      const disabled = action.disabled
        ? typeof action.disabled === 'function'
          ? action.disabled(scope)
          : action.disabled
        : false

      const hidden = action.hidden
        ? typeof action.hidden === 'function'
          ? action.hidden(scope)
          : action.hidden
        : false

      const IconCom = action.icon ? <Icon icon={action.icon} /> : null

      const TextCom = action.text ? (
        typeof action.text === 'function' ? (
          action.text(scope) ? (
            <span>{action.text(scope)}</span>
          ) : null
        ) : (
          <span>{action.text}</span>
        )
      ) : null

      return !hidden ? (
        <ElButton
          type={type}
          link={action.link ?? true}
          disabled={disabled}
          loading={actionBtnLoadingMap[action.name + scope.$index]}
          v-hasPermi={action.permission ?? ['']}
          onClick={() => handleActionBtnClick(action, scope)}
        >
          {IconCom}
          {TextCom}
        </ElButton>
      ) : null
    })
  }

  /** 下拉菜单 */
  function operationDropdown(scope) {
    const showDropdown =
      props.actionsColumn?.actions.length &&
      props.actionsColumn?.limit &&
      props.actionsColumn?.actions.length > props.actionsColumn?.limit

    const items = props.actionsColumn?.actions.slice(props.actionsColumn.limit)

    return showDropdown ? (
      <ElDropdown class="more" size="small">
        {{
          default: () => {
            return (
              <ElButton type="primary" link>
                <span>{t('action.more')}</span>
                <Icon icon="ep:d-arrow-right" size={14} />
              </ElButton>
            )
          },
          dropdown: () => (
            <ElDropdownMenu>
              {items?.map((item) => {
                const disabled = item.disabled
                  ? typeof item.disabled === 'function'
                    ? item.disabled(scope)
                    : item.disabled
                  : false

                const hidden = item.hidden
                  ? typeof item.hidden === 'function'
                    ? item.hidden(scope)
                    : item.hidden
                  : false

                return !hidden ? (
                  <ElDropdownItem key={item.name} disabled={disabled} onClick={() => handleActionBtnClick(item, scope)}>
                    {typeof item.text === 'function' ? item.text(scope) : item.text}
                  </ElDropdownItem>
                ) : null
              })}
            </ElDropdownMenu>
          )
        }}
      </ElDropdown>
    ) : null
  }

  return (
    <>
      {props.showActions ? (
        <ElTableColumn label="操作" className="table-action">
          {{ default: (scope) => [operationsBtns(scope), operationDropdown(scope)] }}
        </ElTableColumn>
      ) : null}
    </>
  )
}

OperationColumnCom.props = operationProps

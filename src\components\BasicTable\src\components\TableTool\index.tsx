import { ElButton } from "element-plus"
import { toolbarProps, type ToolbarProps } from './types'
import { Icon } from '@/components/Icon'
import { SetupContext } from "vue"


/**
 * 表格工具栏
 */
export default function TableToolCom(props: ToolbarProps, context: SetupContext) {
  return (<div class="table-tool">
    <div class="tool-btns">
      {
        props.buttons.map(btn =>
        <ElButton key={btn.name} type={btn.type} onClick={() => context.emit('click', btn)} plain={btn.plain ?? true} class={btn.name}>
          <Icon icon={btn.icon} color={btn.iconColor} size={btn.iconSize} />
          <span>{btn.text}</span>
        </ElButton>)
      }
    </div>
    <div class="tool-utils"></div>
  </div>)
}

TableToolCom.props = toolbarProps
TableToolCom.emits = ['click']


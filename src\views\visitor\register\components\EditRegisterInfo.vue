<template>
  <div class="edit-approval">
    <BasicModal :title="title" @register="register" @confirm="handleConfirm" @close="handleClose">
      <BasicForm ref="formRef" :form-schema="formSchema" />
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { BasicModal } from '@/components/BasicModal'
import { useModalInner } from '@/components/BasicModal/hooks/useModal'
import { BasicForm } from '@/components/BasicForm'
import type { IBasicFormExpose, IFormSchema } from '@/components/BasicForm/types'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { createApprovalRecordAPI, agreeApprovalAPI } from '@/api/visitor'
import type { IApprovalRecord } from '@/api/visitor/types'
import { useMessage } from '@/hooks/web/useMessage'
import { getAllUser } from '@/api/system/user'

defineOptions({
  name: 'EditApproval'
})

const emit = defineEmits<{
  (e: 'submit:success'): void
  (e: 'reject'): void
}>()

const message = useMessage()

/** 表单组件实例 */
const formRef = ref<IBasicFormExpose>()

/** 头像临时地址 */
const avatar = ref<string>('')

/** 是否为编辑或查看模式 */
const isNoAdd = ref(false)

/** 审批记录的id */
const recordId = ref()

/** 审批状态 */
const status = ref()

/** 表单配置 */
const formSchema = reactive<IFormSchema>({
  labelWidth: '80px',
  formItems: [
    {
      field: 'visitorName',
      label: '访客姓名',
      component: 'Input',
      placeholder: '请输入访客姓名',
      rules: { required: true, message: '请输入访客姓名', trigger: 'blur' }
    },
    {
      field: 'sex',
      label: '性别',
      component: 'RadioGroup',
      defaultValue: 1,
      options: getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX),
      rules: { required: true, message: '性别为必填项', trigger: 'change' }
    },
    {
      field: 'phone',
      label: '联系方式',
      component: 'Input',
      placeholder: '请输入联系方式',
      rules: {
        required: true,
        validator(_rule, value, callback) {
          const phoneReg = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/
          if (!value) return callback(new Error('联系方式为必填项！'))
          if (!phoneReg.test(value)) return callback(new Error('联系方式格式不正确！'))
          callback()
        },
        trigger: 'blur'
      }
    },
    {
      field: 'userCompany',
      label: '来访单位',
      component: 'Input',
      placeholder: '请输入来访人单位',
      rules: { required: true, message: '来访人单位为必填项', trigger: 'blur' }
    },
    {
      field: 'userId',
      label: '受访人',
      component: 'Select',
      options: getAllUser.bind(null, { systemType: 0 }),
      componentProps: {
        optionsLabelField: 'username'
      },
      placeholder: '请选择受访人',
      rules: { required: true, message: '受访人姓名为必填项', trigger: 'change' }
    },
    {
      field: 'idCard',
      label: '身份证',
      component: 'Input',
      placeholder: '请输入身份证',
      rules: {
        required: true,
        validator(_rule, value, callback) {
          const idCard = /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/

          if (!value) return callback(new Error('身份证为必填项！'))
          if (!idCard.test(value)) return callback(new Error('身份证格式不正确！'))
          callback()
        },
        trigger: 'blur'
      }
    },
    {
      field: 'startTime',
      label: '开始时间',
      component: 'DataTimePicker',
      placeholder: '请选择开始时间',
      componentProps: {
        type: 'datetime',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        disabledDate(date) {
          return date < new Date()
        }
      },
      rules: { required: true, message: '开始时间为必填项', trigger: 'change' }
    },
    {
      field: 'endTime',
      label: '结束时间',
      component: 'DataTimePicker',
      placeholder: '请选择结束时间',
      componentProps: {
        type: 'datetime',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        disabledDate(date) {
          return date < new Date()
        }
      },
      rules: { required: true, message: '结束时间为必填项', trigger: 'change' }
    },
    {
      field: 'reason',
      label: '来访事由',
      component: 'Input',
      placeholder: '请输入...',
      componentProps: {
        type: 'textarea',
        rows: 4
      },
      colProps: {
        span: 24
      },
      rules: { required: true, message: '来访事由为必填项', trigger: 'blur' }
    }
  ]
})

/** 标题 */
const title = computed(() => (isNoAdd.value ? '访客信息' : '填写登记表'))

const [register, { setModalProps }] = useModalInner((data) => {
  const { action, row } = data as any
  console.log('🚀 ~ const[register]=useModalInner ~ row ====> ', row)

  if (action === 'view' || action === 'edit') {
    status.value = row.status
    recordId.value = row.id
    isNoAdd.value = true
    formRef.value?.disabledFormFields()
    formRef.value?.setFormFieldsValue(row)
  }

  setModalProps?.({
    footerVisible: !isNoAdd.value || !status.value
  })
})

/** 关闭对话框 */
const handleClose = () => {
  isNoAdd.value = false
  if (!isNoAdd.value) {
    URL.revokeObjectURL(avatar.value)
  }
  avatar.value = ''
}

/* 提交表单 */
const handleConfirm = async () => {
  console.log(setModalProps)
  setModalProps?.({
    confirmLoading: true
  })
  try {
    if (isNoAdd.value) {
      // confirmLoading.value = true
      await agreeApprovalAPI(recordId.value)
    } else {
      const { isValid, formData } = (await formRef.value?.customValidate()) as {
        isValid: boolean
        formData: IApprovalRecord
      }
      if (!isValid) return

      setModalProps?.({
        confirmLoading: true
      })

      const formdata = new FormData()

      for (const key in formData) {
        formdata.append(key, formData[key])
      }

      formdata.append('visitorType', '3')

      await createApprovalRecordAPI(formdata)
    }

    message.success('操作成功')

    emit('submit:success')
  } catch (error) {
    console.log(error)
  } finally {
    setModalProps?.({
      confirmLoading: false
    })
  }
}
</script>

<style lang="scss" scoped>
.edit-approval {
  .car-province {
    --el-select-width: 70px;
  }

  .car-province {
    & + .car-city {
      --el-select-width: 60px;

      margin-left: 10px;
    }
  }

  .num-input {
    --el-input-width: 40px;

    margin-left: 10px;

    &:deep(.el-input__inner) {
      text-align: center;
    }
  }

  .avatar {
    width: 148px;
    height: 148px;
    object-fit: contain;
  }

  .avatar-uploader {
    &:deep(.el-upload) {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      transition: var(--el-transition-duration-fast);
    }

    &:deep(.el-upload):hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      width: 178px;
      height: 178px;
      font-size: 28px;
      color: #8c939d;
      text-align: center;
    }
  }
}
</style>

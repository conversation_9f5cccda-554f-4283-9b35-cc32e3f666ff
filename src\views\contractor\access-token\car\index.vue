<template>
  <div class="access-car-token">
    <Table ref="tableRef" :type="1" v-bind="tableConfig" @create-card="handleCreateCard" />
    <EditModal ref="editModalRef" :type="1" @register="register" @submit:success="tableRef?.tableInstance?.refresh" />
    <PrintCardModal @register="registerCardModal">
      <div class="card-content car">
        <h1>{{ rowData.carNumber }}</h1>
      </div>
      <p class="period">{{ rowData.startTime }}-{{ rowData.endTime }}</p>
      <template #back>
        <div class="card-content back">
          <p>1. 本证只限有效期间使用,过期作废。</p>
          <p>2. 本职使用完毕,应立即归还发证部门。</p>
          <p>3. 要妥善保管持证，不得外借、损失,如有丢失立即报告发证机关。</p>
          <p>4. 本证如遇遗失,应立即报发证部门补发</p>
          <p>5. 本证只限于出入本厂大门用,一车一证,置驾驶室风挡玻璃右下角。</p>
        </div>
      </template>
    </PrintCardModal>
  </div>
</template>

<script setup lang="ts">
import Table from '../components/Table.vue'
import { ITableSchema } from '@/components/BasicTable/types'
import EditModal from '../components/EditModal.vue'
import { useModal } from '@/components/BasicModal/hooks/useModal'
import PrintCardModal from '../components/PrintCardModal.vue'

defineOptions({
  name: 'ConAccessCarToken'
})

interface Row {
  username: string
  sex: number
  carNumber: string
  avatar: string
  company: string
  projectName: string
  projectComeName: string
  startTime: string
  endTime: string
  status: number
  type: number
}

/* 编辑对话框组件实例 */
const editModalRef = ref<Nullable<InstanceType<typeof EditModal>>>(null)

/* 表格组件实例 */
const tableRef = ref<Nullable<InstanceType<typeof Table>>>(null)

const [register, { openModal }] = useModal()
const [registerCardModal, { openModal: openCardModal }] = useModal()

/** 表格含数据 */
const rowData = ref()

const tableConfig: ITableSchema = {
  columns: [
    {
      label: '车主',
      prop: 'username',
      enableSearch: true,
      searchFormItemProps: {
        placeholder: '请输入车主姓名'
      }
    },
    {
      label: '车牌号',
      prop: 'carNumber',
      render({ row }) {
        return row.province + row.carNumber
      }
    }
  ],
  toolbar: [
    {
      name: 'add',
      text: '新增',
      type: 'primary',
      icon: 'ep:plus',
      onClick() {
        openModal({
          beforeOpen: () => {
            editModalRef.value?.resetFields()
          }
        })
      }
    }
  ]
}

/** 生成临时出入证 */
const handleCreateCard = (row: Row) => {
  if (!row) return

  rowData.value = row

  openCardModal({
    data: row
  })
}
</script>

<style lang="scss" scoped></style>

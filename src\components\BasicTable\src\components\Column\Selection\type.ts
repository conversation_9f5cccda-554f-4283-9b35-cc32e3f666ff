import type { TableColumnCtx } from 'element-plus'
import { ExtractPropTypes, PropType } from 'vue'

/**
 * props
 */
export const selectionProps = {
  allowSelection: {
    type: Boolean,
    default: false
  },
  selectable: {
    type: Function as PropType<TableColumnCtx<any>['selectable']>
  },
  reserveSelection: {
    type: Boolean,
    default: true
  }
}

export type SelectionProps = ExtractPropTypes<typeof selectionProps>

<template>
  <div class="matter-table">
    <BasicTable ref="basicTableRef" :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'
import { approvalMatterByStatusAPI } from '@/api/approval'
import { DICT_TYPE, getDictLabel, getDictObj, getIntDictOptions } from '@/utils/dict'

defineOptions({
  name: 'MatterTable'
})

const props = defineProps<{
  userId?: number
  approvalStatus: number
}>()

const emit = defineEmits<{
  (e: 'open:detail', data: any): void
}>()

const tableConfig: ITableSchema = {
  columns: [
    {
      label: '事件名称',
      prop: 'event',
      enableSearch: true
    },
    {
      label: '授权类型',
      prop: 'authorizeType',
      dictTag: true,
      dictType: DICT_TYPE.GUARDED_ENTRANCE_AUTHORISATION_METHOD,
      enableSearch: true,
      searchFormItemProps: {
        component: 'Select',
        options: getIntDictOptions(DICT_TYPE.GUARDED_ENTRANCE_AUTHORISATION_METHOD)
      }
    },
    {
      label: '状态',
      prop: 'status',
      dictTag: true,
      dictType: DICT_TYPE.MATTER_STATUS
    },
    {
      label: '创建时间',
      prop: 'createTime'
    }
  ],
  beforeFetch() {
    return {
      userId: props.userId,
      approvalStatus: props.approvalStatus
    }
  },
  apiFn: approvalMatterByStatusAPI,
  ortherHeight: 55,
  searchFormSchema: {
    formItems: [
      {
        field: 'systemType',
        label: '系统',
        component: 'Select',
        placeholder: '请选择系统类型',
        options: getIntDictOptions(DICT_TYPE.SYSTEM_TYPE)
      }
    ]
  },
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text: ({ row }) => {
          return getDictLabel(DICT_TYPE.MATTER_DETAIL_STATUS, row.detailStatus)
        },
        name: 'detail',
        link: true,
        permission: ['approval:detail'],
        onClick(scope) {
          // scope.row.detailStatus = 1
          emit('open:detail', scope.row)
        },
        type: ({ row }) => {
          const matterDetailDict = getDictObj(DICT_TYPE.MATTER_DETAIL_STATUS, row.detailStatus)
          return matterDetailDict?.colorType ?? 'default'
        }
      }
    ]
  }
}

const basicTableRef = ref<IBasicTableExpose>()

const getTableUnreadCount = () => {
  return basicTableRef.value?.getResponseData()?.unReadCount
}

/** 未读数量减一 */
const cutUnReadCount = () => {
  if (basicTableRef.value) {
    basicTableRef.value.getResponseData().unReadCount > 0 && basicTableRef.value.getResponseData().unReadCount--
  }
}

const refresh = () => {
  basicTableRef.value?.refresh()
}

defineExpose({
  getTableUnreadCount,
  refresh,
  cutUnReadCount
})
</script>

<style lang="scss" scoped></style>

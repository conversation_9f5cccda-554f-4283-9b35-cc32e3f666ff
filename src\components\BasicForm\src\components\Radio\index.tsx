import { ElRadio, ElRadioGroup } from 'element-plus'
import { RadioEmits, radioProps, RadioProps } from './types'
import { SetupContext } from 'vue'

/**
 * 单选框
 */
export default function RadioComp(props: RadioProps, { emit }: SetupContext<RadioEmits>) {
  return (
    <ElRadioGroup onChange={(value) => emit('change', value)}>
      {props.options.map((item) => (
        <ElRadio value={item.value} key={item.value}>
          {item.label}
        </ElRadio>
      ))}
    </ElRadioGroup>
  )
}

RadioComp.props = radioProps
RadioComp.emits = ['change']

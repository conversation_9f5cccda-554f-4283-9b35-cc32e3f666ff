<template>
  <div class="message-box">
    <!--  悬浮球 -->
    <LevitatedSphere @click="openMessageListModal" />
    <!--  消息弹窗 -->

    <MessageModal ref="messageModalRef" v-bind="$props" @register="register" @close="$emit('close')">
      <slot></slot>
    </MessageModal>

    <!-- 消息列表 -->
    <MessageListModal @register="registerMessageListModal" />
  </div>
</template>

<script setup lang="ts">
import LevitatedSphere from '../components/LevitatedSphere.vue'
import MessageModal from '../components/MessageModal.vue'
import MessageListModal from '../components/MessageListModal.vue'
import { useMessageModalInner, useMessageModal } from '../hooks/useMessageModal'
import { propTypes } from '@/utils/propTypes'

defineOptions({
  name: 'MessageBox'
})

defineProps({
  title: propTypes.string
})

const [register] = useMessageModalInner()

const [registerMessageListModal, { openMessageModal: openMessageListModal }] = useMessageModal()

/** 消息弹窗组件实例 */
const messageModalRef = ref<InstanceType<typeof MessageModal>>()
</script>

<style lang="scss" scoped>
.message-box {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 9;
  min-height: 200px;
}
</style>

<template>
  <div class="card-item">
    <el-card shadow="hover">
      <div class="card-header" ref="cardHeaderRef">
        <img :src="imageCpt(data)" ref="imageRef" alt="hdx" class="image" />
        <div class="card-action">
          <div class="card-action-wrap">
            <div class="card__status">
              <DictTag :type="cardItemField.status.dictType ?? ''" :value="data[cardItemField.status.prop]" />
            </div>
            <el-dropdown trigger="click" size="small">
              <span class="el-dropdown-link">
                <Icon class="card-action__icon" icon="ep:more-filled" color="#606266" :size="18" />
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="action in actions" :key="action.name" @click="() => action.onClick(data)">
                    {{ action.text }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <h2 class="card__title" :style="{ width: titleWidthCpt }">
            <el-tooltip effect="dark" :content="data[cardItemField.title.prop]" placement="top">
              {{ data[cardItemField.title.prop] }}
            </el-tooltip>
          </h2>
        </div>
      </div>
      <div class="card-desc">
        <slot name="desc" v-bind="{ data }"></slot>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import type { ICardItemField, IActionItem } from '../../types'
import DictTag from '@/components/DictTag/src/DictTag.vue'
import { Icon } from '@/components/Icon'

defineOptions({
  name: 'CardItem'
})

const props = defineProps<{
  cardItemField: ICardItemField
  data: any
  actions?: IActionItem[]
}>()

const cardHeaderRef = ref<HTMLElement>()
const imageRef = ref<HTMLElement>()

/** 显示的图片 */
const imageCpt = computed(() => (data) => {
  return typeof props.cardItemField.image === 'function' ? props.cardItemField.image(data) : props.cardItemField.image
})

/** 标题的宽度 */
const titleWidthCpt = computed(() => {
  if (!cardHeaderRef.value || !imageRef.value) return 'auto'
  return cardHeaderRef.value?.clientWidth - imageRef.value?.clientWidth + 'px'
})
</script>

<style lang="scss" scoped>
.card {
  &-item {
    height: 100%;

    .el-card {
      height: 100%;

      &:deep(.el-card__body) {
        height: 100%;
      }
    }
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: start;

    .image {
      width: 100px;
      height: 100px;
      object-fit: contain;
    }
  }

  &-action {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    &-wrap {
      display: flex;
      align-items: center;
    }

    &__icon {
      margin-left: 8px;
      cursor: pointer;
      transform: rotate(90deg);
    }
  }

  &__title {
    margin-top: 35px;
    overflow: hidden;
    font-size: 16px;
    font-weight: 500;
    text-align: end;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-desc {
    // &::slotted(div) {
    font-size: 14px;
    // }
  }
}
</style>

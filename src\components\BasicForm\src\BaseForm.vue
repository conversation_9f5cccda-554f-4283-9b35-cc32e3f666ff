<template>
  <div class="base-form" ref="baseFormRef">
    <el-form
      :model="formData"
      :rules="formRules"
      ref="formRef"
      :label-width="formSchema.labelWidth"
      :inline="formSchema.inline"
      :disabled="disabled"
    >
      <!-- 搜索栏表单项 -->
      <el-row :gutter="formSchema.rowProps?.gutter">
        <template v-for="formItem in formSchema?.formItems" :key="formItem.field">
          <el-col
            v-if="!formItem.hidden"
            :span="
              formItem.colProps?.span !== undefined
                ? formItem.colProps?.span
                : formSchema.colProps?.span !== undefined
                  ? formSchema.colProps?.span
                  : 12
            "
          >
            <!-- <template v-for="formItem in formSchema?.formItems" :key="formItem.field"> -->
            <el-form-item
              ref="formItemRef"
              :label="formItem.label"
              :label-width="formItem.labelWidth"
              :prop="formItem.field"
              :rules="formItem.rules"
            >
              <!-- 输入框 -->
              <el-input
                v-if="formItem.component === 'Input'"
                v-model="formData[formItem.field]"
                :placeholder="formItem.placeholder"
                :disabled="typeof formItem.disabled === 'function' ? formItem.disabled(formData) : formItem.disabled"
                @change="formItem.onChange"
                :clearable="formItem.clearable ?? true"
                v-bind="formItem.componentProps"
              />

              <!-- 数字输入框 -->
              <el-input-number
                v-else-if="formItem.component === 'InputNumber'"
                v-model="formData[formItem.field]"
                v-bind="formItem.componentProps"
                @change="formItem.onChange"
              />

              <!-- Select选择器 -->
              <el-select
                v-else-if="formItem.component === 'Select'"
                v-model="formData[formItem.field]"
                :placeholder="formItem.placeholder"
                :disabled="typeof formItem.disabled === 'function' ? formItem.disabled(formData) : formItem.disabled"
                :clearable="formItem.clearable ?? true"
                v-bind="formItem.componentProps"
                @change="formItem.onChange"
              >
                <el-option
                  v-for="item in typeof formItem.options === 'function'
                    ? selectOptions[formItem.field]
                    : formItem.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="typeof formItem.disabled === 'function' ? formItem.disabled(formData) : formItem.disabled"
                />
              </el-select>

              <!-- 树下拉选择器 -->
              <el-tree-select
                v-else-if="formItem.component === 'TreeSelect'"
                v-model="formData[formItem.field]"
                :placeholder="formItem.placeholder"
                v-bind="formItem.componentProps"
              />

              <!-- 单选框组 -->
              <el-radio-group
                v-model="formData[formItem.field]"
                v-bind="formItem.componentProps"
                @change="formItem.onChange"
                v-else-if="formItem.component === 'RadioGroup'"
              >
                <el-radio :value="radio.value" v-for="radio in formItem.options as IOption[]" :key="radio.value">{{
                  radio.label
                }}</el-radio>
              </el-radio-group>

              <!-- 级联选择器 -->
              <el-cascader
                v-else-if="formItem.component === 'Cascader'"
                v-model="formData[formItem.field]"
                :options="typeof formItem.options === 'function' ? cascaderOptions[formItem.field] : formItem.options"
                :clearable="formItem.clearable ?? true"
                :placeholder="formItem.placeholder"
                :disabled="typeof formItem.disabled === 'function' ? formItem.disabled(formData) : formItem.disabled"
                v-bind="formItem.componentProps"
                @change="formItem.onChange"
              />

              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="formItem.component === 'DataTimePicker'"
                v-model="formData[formItem.field]"
                :placeholder="formItem.placeholder"
                :default-value="new Date(Date.now())"
                :size="formItem.size"
                v-bind="formItem.componentProps"
              />
              <!-- 手动上传 -->
              <el-upload
                v-if="formItem.component === 'ManualUpload'"
                class="avatar-uploader"
                :auto-upload="false"
                action="#"
                list-type="picture-card"
                v-bind="formItem.componentProps"
                :on-change="
                  (file, fileList) => {
                    formData[formItem.field] = file.raw
                    formItem.onChange && formItem.onChange({ file, fileList })
                  }
                "
                :on-remove="
                  (file, fielList) => {
                    formData[formItem.field] = null
                    formItem.componentProps?.onRemove && formItem.componentProps.onRemove(file, fielList)
                  }
                "
              >
                <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <!-- 按钮 -->
              <el-button
                v-else-if="formItem.component === 'Button'"
                :plain="formItem.componentProps?.plain || true"
                :type="formItem.componentProps?.type"
                :disabled="typeof formItem.disabled === 'function' ? formItem.disabled(formData) : formItem.disabled"
                @click="formItem.componentProps?.onClick?.(formData)"
              >
                <Icon
                  :icon="formItem.componentProps?.icon"
                  v-if="formItem.componentProps?.icon"
                  :color="formItem.componentProps?.iconColor"
                />
                <span v-if="formItem.componentProps?.text">{{ formItem.componentProps?.text }}</span>
              </el-button>
              <!-- 插槽 -->
              <slot :name="formItem.slot" v-bind="{ formData, field: formItem.field }" v-else-if="formItem.slot"></slot>
            </el-form-item>
            <!-- </template> -->
          </el-col>
        </template>
        <slot name="col"></slot>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { FormRules, ElForm } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { IFormSchema, ICascaderOption, IOption } from '../types'
defineOptions({
  name: 'BaseForm'
})

const props = defineProps<{
  formSchema: IFormSchema
  formRules?: FormRules
}>()

/* 表单组件实例 */
const formRef = ref<InstanceType<typeof ElForm>>()
const baseFormRef = ref<HTMLDivElement>()

/* 表单收集的数据 */
const formData = ref<Record<string, any>>({})
/* 级联选择器选项 */
const cascaderOptions = ref<Record<string, ICascaderOption[]>>({})
/* select选择器选项 */
const selectOptions = ref<Record<string, IOption[]>>({})
/** 是否禁用所有表单项 */
const disabled = ref(false)

/* 获取级联选择器的选项 */
const getFormItemOptions = () => {
  props.formSchema?.formItems?.forEach((item) => {
    if (item.options && typeof item.options === 'function') {
      if (item.component === 'Cascader') {
        item
          .options()
          .then((res) => {
            cascaderOptions.value[item.field] = structureCasOptions(res)
          })
          .catch((error) => {
            console.error(error)
          })
      } else if (item.component === 'Select') {
        console.log(item)

        item.options().then((res: any[] | { list: any[] }) => {
          console.log(res)
          const dataList = (res as any[]).length ? res : (res as { list: any[] }).list
          selectOptions.value[item.field] = (dataList as any[]).map((data) => {
            if (item.componentProps?.optionsLabelField && item.componentProps?.optionsValueField) {
              return {
                label: data[item.componentProps?.optionsLabelField],
                value: data[item.componentProps?.optionsValueField]
              }
            } else {
              if (item.componentProps?.optionsLabelField) {
                return {
                  label: data[item.componentProps?.optionsLabelField],
                  value: data.id
                }
              } else if (item.componentProps?.optionsValueField) {
                return {
                  label: data[item.field],
                  value: data[item.componentProps?.optionsValueField]
                }
              } else {
                return {
                  label: data[item.field],
                  value: data.id
                }
              }
            }
          })
        })
      }
    }
  })
}

/* 将列表数据构造cascaderoptions所需要的树结构数据 */
function structureCasOptions(arr: any[], parentId = 0) {
  let options: ICascaderOption[] = []

  for (const item of arr) {
    const option: ICascaderOption = {
      label: item.name,
      value: item.id,
      children: []
    }

    if (item.parentId === parentId) {
      let children = structureCasOptions(arr, item.id)
      if (children.length) {
        option.children = children
      }
      options.push(option)
    }
  }

  return options
}

/* 校验表单字段 */
const validate = async <R extends Record<string, any>>(
  cb?: (isValid: boolean, formData) => void
): Promise<{ isValid: boolean; formData: R }> => {
  const isValid = await formRef.value?.validate()
  cb && cb(isValid as boolean, formData.value)
  return {
    isValid: isValid as boolean,
    formData: formData.value as R
  }
}

/* 重置表单 */
const resetFields = () => formRef.value?.resetFields()

/* 设置表单字段得默认值 */
const setFormFieldsDefaultValue = () => {
  props.formSchema.formItems?.forEach((item) => {
    if (item.defaultValue !== undefined) {
      formData.value[item.field] = item.defaultValue
    }
  })
}

/* 设置表单字段的值 */
const setFormFieldsValue = (data: Record<string, any>) => {
  console.log(data)
  Object.keys(data).forEach((key) => {
    formData.value[key] = data[key]
  })
}

/** 禁用表单项 */
const disabledFormFields = () => {
  disabled.value = true
}

onMounted(() => {
  setFormFieldsDefaultValue()
  getFormItemOptions()
})

defineExpose({
  resetFields,
  validate,
  formData,
  setFormFieldsValue,
  disabledFormFields
})
</script>

<style lang="scss" scoped>
.base-form {
  .el-form-item {
    // width: 100%;
    margin-right: 15px;

    &:deep(.el-cascader) {
      width: 100%;

      .el-input {
        --el-input-width: 100%;

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          appearance: none !important;
        }

        input[type='number'] {
          appearance: textfield !important;
        }
      }
    }

    &:deep(.el-cascader),
    &:deep(.el-input),
    &:deep(.el-select),
    &:deep(.el-date-editor) {
      min-width: 220px;
    }

    &:deep(.el-input-number) {
      .el-input {
        --el-input-width: auto;

        min-width: auto;
      }
    }

    &:deep(.el-date-editor) {
      --el-date-editor-width: 100%;
    }

    .el-input {
      &:deep(.el-input__inner) {
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          appearance: none !important;
        }

        &[type='number'] {
          appearance: textfield;
        }
      }
    }
  }

  .avatar-uploader {
    &:deep(.el-upload) {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      transition: var(--el-transition-duration-fast);
    }

    &:deep(.el-upload):hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      width: 178px;
      height: 178px;
      font-size: 28px;
      color: #8c939d;
      text-align: center;
    }
  }
}
</style>

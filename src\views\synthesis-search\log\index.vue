<template>
  <div class="log">
    <BasicTable :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'
import * as DeptApi from '@/api/system/dept'
import { DICT_TYPE, getDictObj, getIntDictOptions } from '@/utils/dict'
import { doorLogAPI } from '@/api/synthesize-search'

defineOptions({
  name: 'Log'
})

const tableConfig: ITableSchema = {
  apiFn: doorLogAPI,
  beforeFetch() {
    return {
      systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
    }
  },
  columns: [
    {
      label: '用户名',
      prop: 'username',
      enableSearch: true
    },
    {
      label: '门',
      prop: 'doorName',
      enableSearch: true
    },
    {
      label: '部门',
      prop: 'deptName',
      enableSearch: true,
      searchFormItemProps: {
        field: 'detpId',
        component: 'Cascader',
        componentProps: {
          showAllLevels: false,
          props: {
            emitPath: false
          }
        },
        options: DeptApi.getSimpleDeptList.bind(null, {
          systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
        })
      }
    },
    {
      label: '日志类型',
      prop: 'logType',
      dictTag: true,
      dictType: DICT_TYPE.DOOR_LOG_TYPE,
      enableSearch: true,
      searchFormItemProps: {
        component: 'Cascader',
        componentProps: {
          showAllLevels: false,
          props: {
            emitPath: false
          }
        },
        options: getIntDictOptions(DICT_TYPE.DOOR_LOG_TYPE)
      }
    },
    {
      label: '描述',
      prop: 'logDetail'
    },
    {
      label: '温度',
      prop: 'deptName'
    },
    {
      label: '日期',
      prop: 'dateTime'
    }
  ]
}
</script>

<style lang="scss" scoped></style>

import { IColumnsSchema } from '@/components/BasicTable/types'
import { ExtractPropTypes, PropType } from 'vue'

export const contentColumnsProps = {
  column: {
    type: Object as PropType<IColumnsSchema>,
    default: () => []
  },
  tableData: {
    type: Array as PropType<any[]>,
    default: () => []
  }
}

export type ContentColumnsProps = {
  align?: string
} & ExtractPropTypes<typeof contentColumnsProps>

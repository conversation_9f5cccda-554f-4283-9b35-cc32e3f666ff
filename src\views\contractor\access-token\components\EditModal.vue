<template>
  <div class="edit-modal">
    <BasicModal title="出入证信息录入" @register="register" @confirm="handleConfirm">
      <BasicForm ref="formRef" :form-schema="formSchema">
        <template #car-number="{ formData }">
          <!-- 省选择 -->
          <el-select
            class="car-province"
            v-model="numberPlateInfo.province"
            filterable
            default-first-option
            placeholder="省份"
          >
            <el-option
              :key="province.value"
              :label="province.label"
              :value="province.value"
              v-for="province in getDictOptions(DICT_TYPE.NUMBER_PLATE_PROVINCE)"
            />
          </el-select>
          <!-- 市对应字母选择 -->
          <el-select class="car-city" v-model="numberPlateInfo.city" filterable default-first-option placeholder="">
            <el-option
              :key="latter.value"
              :label="latter.label"
              :value="latter.value"
              v-for="latter in getDictOptions(DICT_TYPE.EN_LETTER)"
            />
          </el-select>
          <!-- 号码输入 -->
          <el-input class="num-input" v-model="inputs.num0" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num1" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num2" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num3" :maxlength="1" placeholder="" />
          <el-input class="num-input" v-model="inputs.num4" :maxlength="1" placeholder="" />
          <el-input
            v-if="formData.carType === 2"
            class="num-input"
            v-model="inputs.num5"
            :maxlength="1"
            placeholder=""
          />
        </template>
      </BasicForm>
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
import { BasicModal } from '@/components/BasicModal'
import { BasicForm } from '@/components/BasicForm'
import { IBasicFormExpose, IFormSchema } from '@/components/BasicForm/types'
import { useModalInner } from '@/components/BasicModal/hooks/useModal'
import { getIntDictOptions, getDictOptions, DICT_TYPE } from '@/utils/dict'
import { projectListAPI } from '@/api/contractor/project'
import { enteringAccessTokenInfoAPI } from '@/api/contractor/access-token'
import { useMessage } from '@/hooks/web/useMessage'

defineOptions({
  name: 'EditModal'
})

const props = defineProps<{
  type: number
}>()

const emit = defineEmits<{
  (e: 'submit:success'): void
}>()

const message = useMessage()

const [register, { closeModal }] = useModalInner()

/** 表单组件实例 */
const formRef = ref<IBasicFormExpose>()

/* 头像临时路径 */
const avatar = ref('')

/* 车牌号码信息 */
const numberPlateInfo = reactive({
  province: '',
  city: '',
  numbers: ''
})

/* 车牌号码输入框 */
let inputs = reactive<Record<string, any>>({
  num0: '',
  num1: '',
  num2: '',
  num3: '',
  num4: '',
  num5: ''
})

const formSchema: IFormSchema = {
  labelWidth: 80,
  formItems: [
    {
      field: 'userName',
      label: '姓名',
      component: 'Input',
      placeholder: '请输入姓名',
      rules: { required: true, message: '请输入姓名', trigger: 'blur' }
    },
    {
      field: 'company',
      label: '单位',
      component: 'Input',
      placeholder: '请填写单位',
      rules: { required: true, message: '请填写单位', trigger: 'blur' }
    },
    {
      field: 'sex',
      label: '性别',
      component: 'Radio',
      options: getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX),
      rules: { required: true, message: '请选择性别', trigger: 'change' }
    },
    {
      field: 'projectId',
      label: '项目',
      component: 'Select',
      options: projectListAPI.bind(null, { pageNo: 1, pageSize: 1000 }),
      placeholder: '请选择项目',
      componentProps: {
        optionsLabelField: 'projectName'
      },
      rules: { required: true, message: '请选择项目', trigger: 'change' }
    },
    {
      field: 'carType',
      component: 'Radio',
      defaultValue: 1,
      label: '车牌类型',
      options: getIntDictOptions(DICT_TYPE.NUMBER_PLATE_TYPE),
      colProps: {
        span: 24
      },
      rules: { required: true, message: '车牌类型为必填项', trigger: 'change' }
    },
    {
      field: 'carNumber',
      label: '车牌号',
      slot: 'car-number',
      hidden: props.type !== 1,
      colProps: {
        span: 24
      },
      rules: {
        required: true,
        validator(_rule, _value, callback) {
          if (
            numberPlateInfo.province === '' &&
            numberPlateInfo.city === '' &&
            Object.values(inputs).filter((num) => num).length < 5
          )
            return callback(new Error('请输入车牌号'))
          callback()
        },
        trigger: 'change'
      }
    },
    {
      field: 'startTime',
      label: '开始时间',
      component: 'DataTimePicker',
      placeholder: '请选择开始时间',
      componentProps: {
        type: 'datetime',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      rules: { required: true, message: '请选择开始时间', trigger: 'change' }
    },
    {
      field: 'endTime',
      label: '结束时间',
      component: 'DataTimePicker',
      placeholder: '请选择结束时间',
      componentProps: {
        type: 'datetime',
        valueFormat: 'YYYY-MM-DD HH:mm:ss'
      },
      rules: { required: true, message: '请选择结束时间', trigger: 'change' }
    },
    {
      field: 'avatar',
      label: '头像',
      hidden: props.type !== 0,
      component: 'ManualUpload',
      onChange({ file }) {
        console.log('🚀 ~ onChange ~ uploadFile ====> ', file)
        if (!file) return
        avatar.value = URL.createObjectURL(file.raw as File)
      },
      componentProps: {
        limit: 1,
        onRemove() {
          avatar.value = ''
        }
      },
      colProps: {
        span: 24
      },
      rules: {
        required: true,
        validator: (_rule, _value, callback) => {
          console.log(avatar.value)
          if (!avatar.value) {
            return callback(new Error('头像为必传项！'))
          }
          callback()
        },
        trigger: 'change'
      }
    }
  ]
}

const handleConfirm = () => {
  console.log('提交')
  if (!formRef.value) return
  formRef.value.customValidate((valid, formData) => {
    if (!valid) return

    const numbers = Object.values(inputs).join('')
    const numberPlate = `${numberPlateInfo.city}${numbers}`
    const data = {
      ...formData,
      type: props.type,
      province: numberPlateInfo.province,
      carNumber: numberPlate
    }

    const formdata = new FormData()
    for (const key in data) {
      formdata.append(key, data[key])
    }

    enteringAccessTokenInfoAPI(formdata)
      .then(() => {
        message.success('添加成功！')
        emit('submit:success')
        closeModal()
      })
      .catch((error) => {
        console.log(error)
      })
  })
}

const resetFields = () => {
  formRef.value?.resetFields()
  Object.keys(numberPlateInfo).forEach((key) => (numberPlateInfo[key] = ''))
  Object.keys(inputs).forEach((key) => (inputs[key] = ''))
}

defineExpose({
  resetFields
})
</script>

<style lang="scss" scoped>
.edit-modal {
  .car-province {
    --el-select-width: 70px;

    min-width: 70px !important;

    & + .car-city {
      --el-select-width: 60px;

      min-width: 60px !important;
      margin-left: 10px;
    }
  }

  .num-input {
    --el-input-width: 40px;

    min-width: 40px !important;
    margin-left: 10px;

    &:deep(.el-input__inner) {
      text-align: center;
    }
  }
}
</style>

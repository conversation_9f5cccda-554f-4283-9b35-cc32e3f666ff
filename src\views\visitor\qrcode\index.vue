<template>
  <div class="visitor-qrcode">
    <div class="visitor-qrcode-pass" v-if="qrcodeStatus === 3">
      <el-result icon="success" title="已登记" sub-title="您已登记过无需重复登记" />
    </div>
    <div class="visitor-qrcode-wrapper" v-else>
      <div class="visitor-qrcode-header">
        <h3 class="title"><Icon icon="ant-design:scan-outlined" :size="22" color="#605ab0" />扫码登记</h3>
      </div>
      <div class="visitor-qrcode-code">
        <canvas ref="canvasRef"></canvas>
      </div>
      <div class="visitor-qrcode-desc"> 请至于门岗处亮码登记 </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import QRCode from 'qrcode'
import { useRoute } from 'vue-router'
import { approvalDetailAPI } from '@/api/visitor'
import gsap from 'gsap'

defineOptions({
  name: 'VisitorQrcode'
})

/** canvas */
const canvasRef = ref<HTMLCanvasElement>()
/** 路由信息对象 */
const route = useRoute()
/** 从表格传过来的行数据 */
const raw = ref<
  Nullable<{
    id: number
    hdx: string
  }>
>(null)
/** 二维码状态 */
const qrcodeStatus = ref(JSON.parse(localStorage.getItem('qrcode_status') as string) || 0)
/** 定时器标识符 */
const timer = ref<Nullable<NodeJS.Timeout>>(null)

/** 生成二维码 */
function genQRCode() {
  const rawString = decodeURI(route.query.raw as string)
  console.log('🚀 ~ genQRCode ~ rawString ====> ', rawString)

  raw.value = JSON.parse(rawString)
  console.log('🚀 ~ genQRCode ~ raw.value ====> ', raw.value)

  if (qrcodeStatus.value === 3 || !canvasRef.value) return

  QRCode.toDataURL(
    rawString,
    {
      scale: 6
    },
    (err, url) => {
      if (err) {
        console.log(err)
      }
      const ctx = canvasRef.value?.getContext('2d')
      if (!ctx) return
      const codeImage = new Image()
      const logoImage = new Image()
      codeImage.src = url
      logoImage.src = new URL('@/assets/imgs/yudian-logo.png', import.meta.url).href
      codeImage.onload = function () {
        canvasRef.value!.width = codeImage.width
        canvasRef.value!.height = codeImage.height
        ctx.drawImage(codeImage, 0, 0)
      }
      logoImage.onload = function () {
        ctx.drawImage(logoImage, canvasRef.value!.width / 2 - 22.5, canvasRef.value!.height / 2 - 15, 45, 30)
      }
    }
  )
}

/** 获取二维码状态 */
async function getQRCodeStatus() {
  console.log(raw.value)
  if (!raw.value) return
  try {
    const res = await approvalDetailAPI(raw.value.id)
    console.log(res)
    qrcodeStatus.value = res.status
    if (qrcodeStatus.value === 3) {
      localStorage.setItem('qrcode_status', JSON.stringify(3))
      timer.value && clearInterval(timer.value)
    }
  } catch (error) {
    console.log(error)
  }
}

watchEffect(() => {
  genQRCode()
})

onMounted(async () => {
  await getQRCodeStatus()
  if (qrcodeStatus.value !== 3) {
    timer.value = setInterval(getQRCodeStatus, 2000)
  }
})

onBeforeUnmount(() => {
  localStorage.removeItem('qrcode_status')
  timer.value && clearInterval(timer.value)
})
</script>

<style lang="scss" scoped>
.visitor-qrcode {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;

  &-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background-color: #ccc;
    border-radius: 5px;
  }

  &-header {
    margin-bottom: 20px;

    .title {
      display: flex;
      align-items: center;

      .el-icon {
        margin-top: 3px;
        margin-right: 3px;
      }
    }
  }

  &-desc {
    margin-top: 10px;
    color: #fff;
  }
}
</style>

import { DictTag } from '@/components/DictTag'
import { ElTableColumn } from 'element-plus'
import { SetupContext } from 'vue'
import { type ContentColumnsProps, contentColumnsProps } from './types'

/** 内容列 */
export default function ContentColumnsCom(props: ContentColumnsProps, { slots }: SetupContext) {
  return (
    <ElTableColumn
      key={props.column.prop}
      align={props.align ?? props.column.align ?? 'center'}
      prop={props.column.prop}
      width={props.column.width}
      fixed={props.column.fixed}
      showOverflowTooltip={props.column.toolTip ?? true}
    >
      {{
        header: () => {
          const label =
            typeof props.column.label === 'function'
              ? props.column.label(props.tableData, props.column)
              : props.column.label
          return slots.header ? slots.header() : label ? label : null
        },
        default: (scope) => {
          if (props.column.dictTag && ![undefined, null].includes(scope.row[props.column.prop])) {
            return <DictTag type={props.column.dictType as string} value={scope.row[props.column.prop]} />
          } else if (props.column.slot) {
            return slots[props.column.slot]?.(scope)
          } else if (props.column.render) {
            return props.column.render(scope)
          } else {
            return (
              <span>
                {props.column.formatter && typeof props.column.formatter === 'function'
                  ? props.column.formatter(scope)
                  : scope.row[props.column.prop]}
              </span>
            )
          }
        }
      }}
    </ElTableColumn>
  )
}

ContentColumnsCom.props = contentColumnsProps

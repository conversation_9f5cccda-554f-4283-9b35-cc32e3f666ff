<template>
  <div class="person-table">
    <h3 class="title">{{ title }}</h3>
    <BasicTable ref="basicTableRef" :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import type { IPerson } from '../types'
import type { ITableSchema } from '@/components/BasicTable/types'
import { useShuttle } from '../hooks/useShuttle'

import type { Ref } from 'vue'

import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'

defineOptions({
  name: 'PersonTable'
})

const props = defineProps<Partial<ITableSchema>>()

const emits = defineEmits<{
  (e: 'selection-change', selected: any[]): void
}>()

const { type } = useShuttle()

/* ========== 依赖注入 ========== */
const selectedPerson = inject<Ref<IPerson[]>>('selectedPerson')

/* 表格组件实例 */
const basicTableRef = ref<IBasicTableExpose>()

/* 已选择的数据 */
const selectedData = ref<any[]>([])

/* 表格配置 */
const tableConfig: ITableSchema = {
  rowKey: props.rowKey ?? 'userId',
  columns: props.columns || [],
  tableCardStyle: {
    body: {
      padding: '0'
    }
  },
  beforeFetch() {
    return {
      systemType: 0
    }
  },
  apiFn: props.apiFn,
  allowSelection: true,
  selectionChange(selected) {
    selectedData.value = selected
    emits('selection-change', selected)
  },
  checkboxProps: {
    reserveSelection: true,
    selectable(row) {
      const disabled = !selectedPerson?.value.find((item) => {
        if (type === '5') {
          return item.id === row.id || item.authorizeId === row.id
        } else {
          return item.userId === row.userId || item.authorizeId === row.userId
        }
      })
      return disabled
    }
  },
  enableSearch: true,
  searchFormSchema: {
    inline: true,
    hiddenReset: true,
    showText: false,
    formItems: props.searchFormSchema?.formItems || []
  },
  paginationProps: {
    pageSize: 15,
    pagerCount: 5,
    layout: 'total, prev, pager, next'
  },
  ortherHeight() {
    const titleHeight = 42
    const searchHeight = 69
    const cardPadding = 20
    return titleHeight + searchHeight + cardPadding
  }
}

defineExpose({
  basicTableRef
})
</script>

<style lang="scss" scoped>
.person-table {
  height: 100%;

  .title {
    margin-bottom: 10px;
    font-size: 22px;
    font-weight: 400;
    color: #409eff;
  }

  &:deep(.el-pagination) {
    padding-right: 15px;
  }

  .basic-table {
    height: 100%;

    &:deep(.table-search) {
      .table-search__body {
        padding: 10px !important;
      }

      .el-form {
        height: auto;

        .el-form-item {
          margin-right: 8px;
          margin-bottom: 0;
        }
      }
    }

    &:deep(.el-card.table-area) {
      height: calc(100% - 110px);

      .el-card__body {
        height: 100%;

        .el-table {
          display: flex;

          &__inner-wrapper {
            width: 100%;

            &::before {
              width: 0;
            }
          }
        }
      }
    }

    &:deep(.el-input) {
      --el-input-width: 100px;

      min-width: 100px;
    }

    &:deep(.el-cascader) {
      min-width: 155px;

      .el-input {
        --el-input-width: 155px;
      }
    }
  }
}
</style>

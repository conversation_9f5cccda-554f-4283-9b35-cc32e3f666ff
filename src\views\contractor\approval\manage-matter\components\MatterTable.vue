<template>
  <div class="matter-table">
    <BasicTable ref="basicTableRef" :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'
import { approvalMatterByStatusAPI } from '@/api/approval'
import { DICT_TYPE, getDictLabel, getDictObj, getIntDictOptions } from '@/utils/dict'

defineOptions({
  name: 'MatterTable'
})

const props = defineProps<{
  userId?: number
  approvalStatus: number
}>()

const emit = defineEmits<{
  (e: 'open:detail', data: any): void
}>()

const tableConfig: ITableSchema = {
  columns: [
    {
      label: '事件名称',
      prop: 'event',
      enableSearch: true
    },
    {
      label: '授权类型',
      prop: 'authorizeType',
      dictTag: true,
      dictType: DICT_TYPE.GUARDED_ENTRANCE_AUTHORISATION_METHOD,
      enableSearch: true,
      searchFormItemProps: {
        component: 'Select',
        options: getIntDictOptions(DICT_TYPE.GUARDED_ENTRANCE_AUTHORISATION_METHOD)
      }
    },
    {
      label: '状态',
      prop: 'status',
      dictTag: true,
      dictType: DICT_TYPE.MATTER_STATUS
    },
    {
      label: '创建时间',
      prop: 'createTime'
    }
  ],
  beforeFetch() {
    return {
      userId: props.userId,
      systemType: 1,
      approvalStatus: props.approvalStatus
    }
  },
  apiFn: approvalMatterByStatusAPI,
  ortherHeight: 55,
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text: ({ row }) => {
          return getDictLabel(DICT_TYPE.MATTER_DETAIL_STATUS, row.detailStatus)
        },
        name: 'detail',
        link: true,
        permission: ['contractor:approval:detail'],
        onClick(scope) {
          scope.row.detailStatus = 1
          emit('open:detail', scope.row)
        },
        type: ({ row }) => {
          const matterDetailDict = getDictObj(DICT_TYPE.MATTER_DETAIL_STATUS, row.detailStatus)
          return matterDetailDict?.colorType ?? 'default'
        }
      }
    ]
  }
}

const basicTableRef = ref<IBasicTableExpose>()

const getTableUnreadCount = () => {
  return basicTableRef.value?.getResponseData()?.unReadCount
}

const refresh = () => {
  return basicTableRef.value?.refresh()
}

defineExpose({
  getTableUnreadCount,
  refresh
})
</script>

<style lang="scss" scoped></style>

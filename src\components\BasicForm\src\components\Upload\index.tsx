import { ElIcon, ElUpload } from 'element-plus'
import { SetupContext } from 'vue'
import type { UploadEmits } from './types'
import { Plus } from '@element-plus/icons-vue'
import './index.scss'

export default function UploadComp(props, context: SetupContext<UploadEmits>) {
  return (
    <ElUpload
      class="uploader"
      action="#"
      autoUpload={false}
      listType="picture-card"
      onChange={(file, fileList) => context.emit('change', file, fileList)}
      onRemove={(file, fileList) => context.emit('remove', file, fileList)}
    >
      <ElIcon class="uploader-icon">
        <Plus />
      </ElIcon>
    </ElUpload>
  )
}

UploadComp.emits = ['change', 'remove']

<template>
  <div class="authoriztion">
    <ContentWrap class="flex justify-end">
      <!-- 搜索工作栏 -->

      <el-button type="primary" plain @click="handleAuthOrisation">
        <Icon class="mr-5px" icon="mdi:user-check-outline" />
        授权
      </el-button>
      <el-button v-hasPermi="['system:role:create']" plain type="warning" @click="handleRemoveAuth">
        <Icon class="mr-5px" icon="mdi:user-block-outline" />
        解权
      </el-button>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap
      class="auth-content h-[calc(100vh-var(--top-tool-height)-var(--tags-view-height)-var(--app-footer-height)-79px)]"
    >
      <el-row :gutter="10" class="h-full">
        <el-col :span="12">
          <PersonShuttle ref="personShuttleRef" v-bind="psConf" @copy-auth="handleCopyReaderAuth" />
        </el-col>
        <el-col :span="12">
          <ReaderShuttle ref="readerShuttleRef" v-bind="rsConf" @copy-auth="handleCopyPersonAuth" />
        </el-col>
      </el-row>
    </ContentWrap>
    <!-- 复制用户权限对话框 -->
    <CopyUserReader
      ref="copyUserReaderRef"
      :api-fn="printMandateRecordAPI"
      @submit:selected-reader="handleSubmitSelectedReader"
    />
    <!-- 复制用户权限对话框 -->
    <CopyReaderUser
      ref="copyReaderUserRef"
      :api-fn="userListByFingerprintReaderAPI"
      @submit:selected-user="handleSubmitSelectedUser"
    />
    <!-- 授权对话框 -->
    <Authorisation ref="authorisationRef" />
  </div>
</template>

<script setup lang="ts">
import PersonShuttle from '../components/ShuttleTable/PersonShuttle.vue'
import ReaderShuttle from '../components/ShuttleTable/ReaderShuttle.vue'
import CopyUserReader from '../components/Dialog/CopyUserReader.vue'
import Authorisation from '../components/Dialog/Authorisation.vue'

import type { ITableSchema } from '@/components/BasicTable/types'
import * as DeptApi from '@/api/system/dept'
import {
  readerListAPI,
  alreadyHaveFingerPrintPersonsAPI,
  userListByFingerprintReaderAPI,
  printMandateRecordAPI
} from '@/api/authorization'
import CopyReaderUser from '../components/Dialog/CopyReaderUser.vue'
import { ElCheckboxGroup, ElCheckbox } from 'element-plus'
import { DICT_TYPE, getDictObj } from '@/utils/dict'
import { getDictLabel } from '@/utils/dict'
import { useShuttle } from '../hooks/useShuttle'

defineOptions({
  name: 'AuthorizationFingerPrint'
})

interface IConfig {
  sourceConfig: Partial<ITableSchema>
  selectedConfig: Partial<ITableSchema>
}

const {
  type,
  authorisationRef,
  personShuttleRef,
  copyReaderUserRef,
  readerShuttleRef,
  copyUserReaderRef,
  handleCopyReaderAuth,
  handleSubmitSelectedReader,
  handleCopyPersonAuth,
  handleSubmitSelectedUser,
  handleAuthOrisation,
  handleRemoveAuth
} = useShuttle()

/* 指纹渲染函数 */
const generatorFingerprintVNodes = (type: number) => {
  const fingerprintRender = ({ row }) => {
    /** 已选择的指纹 */
    const fingerprintVNodes = row.fingerprintInfoList?.map((fingerprint) => {
      return h(ElCheckbox, {
        label: getDictLabel(DICT_TYPE.FINGERPRINT_TEMPLATE, fingerprint.type),
        value: fingerprint.id,
        border: true,
        checked: !fingerprint.status,
        disabled: !type
      })
    })

    /** 返回渲染函数 */
    return h(
      ElCheckboxGroup,
      {
        size: 'small',
        modelValue: (row.checked ??= []),
        'onUpdate:modelValue': (value: number[]) => {
          row.checked = value
        }
      },
      () => fingerprintVNodes
    )
  }

  return fingerprintRender
}

/* ========== 穿梭框配置 ========== */

/* 人员穿梭框 */
const psConf: IConfig = {
  sourceConfig: {
    title: '人员列表',
    scrollbarAlwaysOn: true,
    apiFn: alreadyHaveFingerPrintPersonsAPI,
    columns: [
      {
        label: '用户名',
        prop: 'username',
        fixed: true,
        enableSearch: true,
        searchFormItemProps: {
          hiddenLabel: true
        }
      },
      {
        label: '部门',
        prop: 'deptName',
        width: 120,
        enableSearch: true,
        searchFormItemProps: {
          field: 'deptId',
          component: 'Cascader',
          hiddenLabel: true,
          componentProps: {
            showAllLevels: false,
            props: {
              emitPath: false
            }
          },
          options: DeptApi.getSimpleDeptList.bind(null, {
            systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
          }),
          onChange(value) {
            console.log(value)
          }
        }
      },
      {
        label: '指纹',
        prop: 'fingerprintInfoList',
        width: 280,
        align: 'left',
        render: generatorFingerprintVNodes(0)
      }
    ],
    searchFormSchema: {
      hiddenReset: true
    }
  },
  selectedConfig: {
    title: '已选人员列表',
    columns: [
      {
        label: '用户名',
        prop: 'username',
        fixed: true,
        width: 120
      },
      {
        label: '部门',
        prop: 'deptName',
        width: 120
      },
      {
        label: '指纹',
        prop: 'fingerprintInfoLIst',
        align: 'left',
        width: 280,
        render: generatorFingerprintVNodes(1)
      }
    ]
  }
}

/* 设备（读卡器...）穿梭框 */
const rsConf: IConfig = {
  sourceConfig: {
    title: '读卡器列表',
    apiFn: readerListAPI,
    beforeFetch() {
      return {
        cardReaderType: +type,
        cardReaderStatus: 2
      }
    },
    columns: [
      {
        label: '设备位置',
        prop: 'deviceLocation',
        enableSearch: true,
        searchFormItemProps: {
          hiddenLabel: true
        }
      },
      {
        label: '安装位置',
        prop: 'installationPosition'
      }
    ],
    searchFormSchema: {
      hiddenReset: true
    }
  },
  selectedConfig: {
    title: '已选读卡器列表',
    columns: [
      {
        label: '设备位置',
        prop: 'deviceLocation'
      }
    ]
  }
}
</script>

<style lang="scss" scoped>
.el-card {
  &.mb-15px.auth-content {
    margin-bottom: 0;

    &:deep(.el-card__body) {
      height: 100%;
    }
  }
}
</style>

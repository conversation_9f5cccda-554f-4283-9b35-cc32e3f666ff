<template>
  <div class="hdx-auth-table">
    <BasicTable :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable } from '@/components/BasicTable'
import { ITableSchema } from '@/components/BasicTable/types'
import { authControllerSettingAPI } from '@/api/synthesize-search'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'HDXAuthTable'
})

const router = useRouter()

const tableConfig: ITableSchema = {
  columns: [
    {
      label: '控制器编号',
      prop: 'controllerNumber'
    },
    {
      label: 'IP地址',
      prop: 'ipAddress'
    },
    {
      label: '安装位置',
      prop: 'installationPosition',
      enableSearch: true
    },
    {
      label: '设备接入位置',
      prop: 'doorName'
    }
  ],
  apiFn: authControllerSettingAPI,
  showActions: true,
  actionsColumn: {
    actions: [
      {
        text: '查看权限状态',
        name: 'view',
        permission: ['synthesis-search:hdx:view'],
        onClick: ({ row }) => {
          console.log(row)
          router.push({
            path: '/synthesis-search/hdx',
            query: {
              id: row.controllerNumber
            }
          })
        }
      }
    ]
  },
  ortherHeight: 71 // el-tabs 的上下内边距、tab高度以及边框高度
}
</script>

<style lang="scss" scoped></style>

import type { Ref } from 'vue'
import type { ButtonType, TagProps, PaginationProps, TableInstance } from 'element-plus'
import type { IFormSchema, IButtonProps, FormItemType } from '@/components/BasicForm/types'

export enum POSITION_ENUM {
  LEFT = 'left',
  RIGHT = 'right',
  TOP = 'top',
  CNETER = 'center'
}

/* 表格结构表 */
export interface ITableSchema {
  title?: string
  /* 表格列 */
  columns: IColumnsSchema[]
  /* 分页信息 */
  paginationProps?: IPaginationProps
  /* 列内容对齐方式 */
  align?: string
  /* 是否一直显示滚动条 */
  scrollbarAlwaysOn?: boolean
  /* search 卡片样式 */
  searchCardStyle?: ICardStyle
  /* table 卡片样式 */
  tableCardStyle?: ICardStyle
  /* 获取表格数据接口 */
  apiFn?: (params?: any) => Record<string, any>
  /* 请求发送前执行的函数 */
  beforeFetch?: (data?: any) => any
  /* 请求发送后执行的函数 */
  afterFetch?: (list: any, columns: any) => any
  /* 不采用api接口方式取得数据 */
  data?: any[] | Ref<any[]>
  /* 是否允许多选 */
  allowSelection?: boolean
  /* 表格选择事件处理函数 */
  selectionChange?: (selected: any[]) => void
  /* 多选列属性 */
  checkboxProps?: ICheckboxProps
  /* 行的唯一标识 */
  rowKey?: string
  /* 表格顶部搜索区域 */
  enableSearch?: boolean
  /* 表格顶部搜索区域表单结构 */
  searchFormSchema?: ITableSearchForm
  /* 工具栏：新增按钮等等 */
  toolbar?: IButtonBtnSchema[]
  /* 是否显示操作栏 */
  showActions?: boolean
  /* 表格操作列 */
  actionsColumn?: IActionsColumnSchema
  /* 表格最大高度需要减去的高度 */
  ortherHeight?: number | (() => number)
}

/* 表格列结构 */
export interface IColumnsSchema {
  /* 字段名称 */
  prop: string
  /* 显示的标题 */
  label: string | ((rows, scope) => string)
  /* 列的宽度 */
  width?: string | number
  /** 是否在搜索栏开启显示 */
  enableSearch?: boolean
  /** 搜索栏表单项属性 */
  searchFormItemProps?: Partial<FormItemType>
  /* 是否固定列 */
  fixed?: boolean | 'left' | 'right'
  /* 是否标签展示 */
  isTag?: boolean
  /** 内容对其方式 */
  align?: 'left' | 'center' | 'right'
  /* 标签类型 */
  tagType?: TagProps['type'] | ((scope: { row?: any; column?: any; $index?: number }) => TagProps['type'])
  /* tag的主题 */
  tagEffect?: TagProps['effect']
  /* 是否显示字典标签 */
  dictTag?: boolean
  /* 字典类型 */
  dictType?: string
  /* 字典键值 */
  dictValueField?: string
  /* 插槽 */
  slot?: string
  /* 格式化内容方法 */
  formatter?: (scope: { row?: any; column?: any; $index?: number }) => any
  /* 是否内容过长显示tooltip */
  toolTip?: boolean
  /* 插槽render函数 */
  render?: (scope: { row?: any; column?: any; $index?: number }) => VNode
}

/* 多选列属性 */
interface ICheckboxProps {
  /* 数据刷新后是否保留选项 */
  reserveSelection?: boolean
  /* 返回值用来决定这一行的 CheckBox 是否可以勾选 */
  selectable?: (row?: any, index?: number) => boolean
}

export interface ITableSearchForm extends IFormSchema {
  /* 是否隐藏搜索按钮 */
  hiddenSearch?: boolean
  /* 是否隐藏重置按钮 */
  hiddenReset?: boolean
  /* 是否显示按钮文本 */
  showText?: boolean
  /* 是否显示按钮图标 */
  showIcon?: boolean
}

/* 按钮结构 */
export interface IButtonBtnSchema extends Partial<Omit<IButtonProps, 'text' | 'disabled'>> {
  /* 按钮名称 */
  name: string
  /* 按钮文本 */
  text?: string
  /* 按钮禁用 */
  disabled?: boolean | ((scope: { row?: any; column?: any; $index?: number }) => boolean)
  /** 按钮图标大小 */
  iconSize?: number
}

/* 基础表格卡片样式 */
interface ICardStyle {
  /* 是否显示卡片的边框 */
  showBorder?: boolean
  /* 卡片body样式 */
  body?: Record<string, any>
}

/**
 * 操作列字段结构
 */
export interface IActionsColumnSchema {
  /* 操作列按钮 */
  actions: ITableButton[]
  /* 超过几个时显示下拉菜单 */
  limit?: number
  /* 操作列的宽度 */
  width?: string | number
}

/* 操作 */
export interface ITableButton extends Omit<IButtonBtnSchema, 'text' | 'type'> {
  /* 按钮类型 */
  type?: ButtonType | ((scope: { row?: any; column?: any; $index?: number }) => ButtonType)
  /* 操作按钮文本内容 */
  text: string | ((scope: { row?: any; column?: any; $index?: number }) => string | boolean)
  /* 文本内容下标字段 */
  textIndexField?: string
  /* 是否展示loading效果 */
  enableLoading?: boolean
  /* 按钮的点击事件回调函数 */
  onClick?: (...args: any[]) => void | Promise<any>
  /* 权限 */
  permission?: string[]
}

/* 分页器属性 */
export interface IPaginationProps {
  pageSize?: number
  pageNo?: number
  pagerCount?: number
  layout?: PaginationProps['layout']
}

/** BasicTable 组件暴露的内容 */
export type IBasicTableExpose = TableInstance & {
  total: number
  tableData: any[]
  getResponseData: () => any
  getList: () => void
  refresh: () => void
  getTableHeight: () => number
  toggleExpandAll: () => void
  getTableDataSource: () => Map<string, any>
}

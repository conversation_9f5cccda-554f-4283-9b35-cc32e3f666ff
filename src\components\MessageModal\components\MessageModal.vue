<template>
  <Transition @enter="handleEnter" @leave="handleLeave">
    <div class="message-modal" v-show="visible">
      <header class="modal-header">
        <!-- 标题 -->
        <div class="title">
          <slot name="title">
            <Icon :icon="icon" :size="iconSize" :color="iconColor" />
            <span class="text">{{ title }}</span>
          </slot>
        </div>
        <!-- 中间区域 -->
        <div class="middle-area">
          <slot name="middle"></slot>
        </div>
        <!-- 关闭按钮 -->
        <slot name="close">
          <Icon :icon="closeIcon" :size="18" class="close" @click="handleCloseModal" />
        </slot>
      </header>
      <main class="content">
        <slot> MessageModal </slot>
      </main>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { getCurrentInstance } from 'vue'
import { propTypes } from '@/utils/propTypes'
import type { IModalMethods, IModalProps } from '../types'
import { gsap } from 'gsap'

/** 当前组件实例 */
const currentInstance = getCurrentInstance()

defineOptions({
  name: 'MessageModal'
})

defineProps({
  title: propTypes.string.def('Title'),
  icon: propTypes.string.def('ep:warning-filled'),
  iconSize: propTypes.number.def(24),
  iconColor: propTypes.string.def('var(--el-color-warning)'),
  closeIcon: propTypes.string.def('ep:close')
})

const emit = defineEmits<{
  (e: 'register', modalMethods: IModalMethods, uid: number): void
  (e: 'close'): void
}>()

/** MessageModal 组件的方法集 */
const methods: IModalMethods = {
  setModalProps,
  emitVisible: undefined
}

/** 消息提示框可见性 */
const visible = ref(false)

/**
 * @description 设置 Modal 的属性
 * @param props
 */
function setModalProps(props: IModalProps) {
  if (Object.hasOwn(props, 'visible')) {
    visible.value = !!props.visible
  }
}

/** 在元素被插入到 DOM 之后的下一帧被调用 */
const handleEnter = () => {
  gsap.fromTo(
    '.message-modal',
    { yPercent: 100, opacity: 0 },
    { yPercent: 0, opacity: 1, duration: 0.3, ease: 'bounce.out' }
  )
}

/** 离开过渡开始时调用 */
const handleLeave = () => {
  gsap.to('.message-modal', { yPercent: 100, opacity: 0, duration: 0.5, ease: 'back' })
}

/** 监听 visible 变化，触发 emitVisible 事件修改 VISIBLE_DATA 中uid对应的值 */
watch(visible, (newVal) => {
  currentInstance && methods.emitVisible?.(newVal, currentInstance.uid)
})

/** 关闭  Modal 弹窗 */
const handleCloseModal = () => {
  emit('close')
  visible.value = false
}

if (currentInstance) {
  emit('register', methods, currentInstance.uid)
}
</script>

<style lang="scss" scoped>
.message-modal {
  position: absolute;
  right: 10px;
  bottom: 10px;
  display: flex;
  width: 20vw;
  min-height: 250px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 0 8px #ccc;
  flex-direction: column;

  .modal-header {
    display: flex;
    height: 45px;
    padding: 0 10px;
    border-bottom: 1px solid #e4e7ed;
    justify-content: space-between;
    align-items: center;

    .title {
      display: flex;
      align-items: center;
      font-size: 18px;

      .v-icon {
        margin-right: 3px;
        transform: translateY(1.5px);
      }
    }

    .close {
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  .content {
    display: flex;
    flex: 1;
    padding: 10px;
  }
}
</style>

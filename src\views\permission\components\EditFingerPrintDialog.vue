<template>
  <div class="edit-fingerprint-dialog">
    <Dialog
      :title="title"
      :close-on-click-modal="!formItemDisabled"
      :close-on-press-escape="!formItemDisabled"
      :show-close="!formItemDisabled"
      v-model="dialogVisible"
    >
      <!-- 表单信息 -->
      <BasicForm ref="formRef" :form-schema="formSchema">
        <template #card-reader="{ formData, field }">
          <BasicTable :table-config="tableConfig">
            <template #radio="{ row }">
              <el-radio-group v-model="formData[field]" @change="handleRadioChange">
                <el-radio :value="row.addressOfCardReader" size="default" />
              </el-radio-group>
            </template>
          </BasicTable>
        </template>
      </BasicForm>
      <!-- 对话框底部取消、确认按钮 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button :disabled="formItemDisabled" @click="handleCancel">取 消</el-button>
          <el-button type="primary" :loading="loading" :disabled="submitDisabled" @click="handleSubmit"
            >确 定</el-button
          >
        </span>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { Dialog } from '@/components/Dialog'
import { BasicForm } from '@/components/BasicForm'
import type { IBasicFormExpose, IFormSchema } from '@/components/BasicForm/types'
import * as UserAPI from '@/api/system/user'
import { getDictOptions, DICT_TYPE, getDictObj } from '@/utils/dict'
import { readerListAPI } from '@/api/authorization'
import { BasicTable } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'
import { addFingerprintAPI, enteringFingerprintAPI } from '@/api/permission'
import { ElMessage } from 'element-plus'
import { useMessage } from '@/hooks/web/useMessage'

/* 指纹录入成功信息类型 */
interface IAuthFingerprintSuccessInfo {
  addressOfCardReader: string
  fingerpringStatusFlag: boolean
  fingerpringTemplate: string
  usageStatusFlag: boolean
}

defineOptions({
  name: 'EditFingerPrintDialog'
})

/* 路由信息对象 */
const route = useRoute()

const message = useMessage()

/* 授权类型 */
const type = (route.meta.query as { type: string })?.type

const emit = defineEmits<{
  (e: 'edit-fingerprint'): void
}>()

/* 录入成功的读卡器信息 */

/* 表单字段校验规则 */
const formRules = {
  userId: [{ required: true, message: '请选择用户', trigger: 'change' }],
  type: [{ required: true, message: '请选择指纹', trigger: 'change' }],
  addressOfCardReader: [{ required: true, message: '请选择读卡器', trigger: 'change' }]
}

/* 表单字段 */
const formSchema: IFormSchema = {
  rules: formRules,
  formItems: [
    {
      component: 'Select',
      field: 'userId',
      label: '用户名',
      placeholder: '请选择用户',
      options: UserAPI.getAllUser.bind(null, {
        systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 0)?.value
      }),
      disabled() {
        return isUpdate.value || formItemDisabled.value
      },
      componentProps: {
        filterable: true,
        optionsLabelField: 'username'
      }
    },
    {
      component: 'Select',
      field: 'type',
      label: '指纹',
      placeholder: '请选择指纹',
      disabled() {
        return isUpdate.value || formItemDisabled.value
      },
      options: getDictOptions(DICT_TYPE.FINGERPRINT_TEMPLATE)
    },
    {
      field: 'addressOfCardReader',
      label: '读卡器',
      slot: 'card-reader',
      colProps: {
        span: 24
      }
    }
  ]
}
/* 表单组件实例 */
const formRef = ref<IBasicFormExpose>()

/* 对话框标题 */
const title = ref('新增指纹')
/* 对话框可见性 */
const dialogVisible = ref(false)
/* 按钮loading */
const loading = ref(false)
/* 是否修改 */
const isUpdate = ref(false)
/* 表格当前行数据 */
const rowData = ref()

/* 是否禁用确认按钮，默认禁用 */
const submitDisabled = ref(true)
/** 是否禁用表单项 */
const formItemDisabled = ref(false)
/* 录入成功的读卡器信息 */
const authFingerpringSuccessInfo = ref<IAuthFingerprintSuccessInfo>()
/* 表格中成功录入指纹的读卡器的信息 */
const authSuccessCardReaderInfoMap = new Map()

/* 表格配置 */
const tableConfig: ITableSchema = {
  columns: [
    {
      label: '',
      prop: 'radio',
      slot: 'radio',
      width: 80
    },
    {
      label: '控制器编号',
      prop: 'controllerNumber'
    },
    {
      label: '设备位置',
      prop: 'deviceLocation'
    },
    {
      label: '读卡器安装位置',
      prop: 'installationPosition'
    }
  ],
  beforeFetch() {
    return {
      cardReaderType: +type,
      cardReaderStatus: 2
    }
  },
  apiFn: readerListAPI,
  enableSearch: false,
  showActions: true,
  actionsColumn: {
    width: 100,
    actions: [
      {
        enableLoading: true,
        text: '录入指纹',
        name: 'entering',
        onClick({ row }) {
          if (!formRef.value?.formData.userId || !formRef.value?.formData.type) {
            return message.warning('用户或指纹不能为空！')
          }

          /** 禁用表单项 */
          formItemDisabled.value = true

          return new Promise((resolve, reject) => {
            enteringFingerprintAPI(row.addressOfCardReader)
              .then((res) => {
                console.log('录入指纹成功', res)
                if (res.code === 200) {
                  ElMessage.success('指纹录入成功！')
                  /* 将当前录入成功的读卡器信息暂存起来 */
                  authFingerpringSuccessInfo.value = res.data
                  submitDisabled.value = false
                  /* 将表格中录入成功的数据暂存起来 */
                  if (!authSuccessCardReaderInfoMap.has(row.addressOfCardReader)) {
                    authSuccessCardReaderInfoMap.set(row.addressOfCardReader, row)
                  }
                  resolve(true)
                } else {
                  ElMessage.error('指纹录入失败！')
                  reject(new Error('指纹录入失败！'))
                }
              })
              .catch((error) => {
                // console.log('指纹录入错误：', error)
                reject(error)
              })
              .finally(() => {
                formItemDisabled.value = false
              })
          })
        },
        disabled({ row }) {
          return row.addressOfCardReader !== formRef.value?.formData.addressOfCardReader
        }
      }
    ]
  }
}

/* 打开对话框 */
const open = (params) => {
  const thumbs = ['thumb', 'indexFinger', 'middleFinger']
  title.value = params.title
  isUpdate.value = params.isUpdate
  dialogVisible.value = true

  if (params.isUpdate) {
    rowData.value = params.row
    nextTick(() => {
      formRef.value?.setFormFieldsValue({
        userId: params.row.userId,
        type: thumbs.indexOf(params.type) + ''
      })
    })
  }
}

/* 关闭对话框 */
const close = () => {
  dialogVisible.value = false
}

const handleRadioChange = (value) => {
  console.log(value)
}

/** 取消 */
const handleCancel = () => {
  dialogVisible.value = false
}

/* 提交 */
const handleSubmit = () => {
  console.log(formRef.value)
  formRef.value?.customValidate?.((isValid, formData) => {
    if (!isValid) return
    loading.value = true

    /* 从已成功录入的指纹信息映射中查找到最后一次录入成功的指纹信息 */
    const cardReaderInfo = authSuccessCardReaderInfoMap.get(authFingerpringSuccessInfo.value?.addressOfCardReader)

    console.log(cardReaderInfo)

    const params = {
      ...formData,
      addressOfCardReader: cardReaderInfo.addressOfCardReader,
      cardReaderId: cardReaderInfo.id,
      fingerprint: 2
    }

    // const apiFn = isUpdate.value ? editFingerprintAPI : addFingerprintAPI
    isUpdate.value && (params.status = rowData.value.status)

    addFingerprintAPI(params)
      .then(() => {
        ElMessage.success('操作成功！')
        close()
        emit('edit-fingerprint')
      })
      .catch((error) => {
        console.log(error)
      })
      .finally(() => {
        loading.value = false
      })
  })
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.edit-fingerprint-dialog {
  &:deep(.el-card__body) {
    padding: 0 !important;
  }
}
</style>

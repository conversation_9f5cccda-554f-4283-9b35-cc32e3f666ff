<template>
  <div class="project">
    <BasicTable ref="basicTableRef" :table-config="tableConfig">
      <template #progress="{ row }">
        <el-progress type="dashboard" :percentage="row.projectFinishPercentage * 100" :width="65" :color="colors">
          <template #default="{ percentage }">
            <span class="percentage-value">{{ percentage }}%</span>
          </template>
        </el-progress>
      </template>
    </BasicTable>
    <ProjectEditDialog ref="projectEditDialogRef" @submit:success="basicTableRef?.refresh" />
  </div>
</template>

<script setup lang="ts">
import { BasicTable, IBasicTableExpose } from '@/components/BasicTable'
import type { ITableSchema } from '@/components/BasicTable/types'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import ProjectEditDialog from './components/ProjectEditDialog.vue'
import { deleteProjectAPI, projectListAPI } from '@/api/contractor/project'
import { getSimpleDeptList } from '@/api/system/dept'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'Project'
})

const router = useRouter()

/* 进度调颜色 */
const colors = [
  { color: '#A3A6AD', percentage: 20 },
  { color: '#409EFF', percentage: 40 },
  { color: '#1989fa', percentage: 60 },
  { color: '#5cb87a', percentage: 80 },
  { color: '#67C23A', percentage: 100 }
]

/* 表格组件实例 */
const basicTableRef = ref<IBasicTableExpose>()

/* 编辑对话框组件实例 */
const projectEditDialogRef = ref<InstanceType<typeof ProjectEditDialog>>()

/* 表格配置 */
const tableConfig: ITableSchema = {
  columns: [
    {
      label: '项目名称',
      prop: 'projectName',
      enableSearch: true
    },
    {
      label: '项目来源',
      prop: 'projectSource'
    },
    {
      label: '项目联系人',
      prop: 'projectUser'
    },
    {
      label: '承包商',
      prop: 'contractorName',
      enableSearch: true,
      searchFormItemProps: {
        field: 'contractorId',
        component: 'Select',
        options: getSimpleDeptList.bind(null, { systemType: 1 }),
        componentProps: {
          optionsLabelField: 'name'
        }
      }
    },
    {
      label: '承包商联系人',
      prop: 'contractorUserName'
    },
    {
      label: '项目状态',
      prop: 'projectIsFinish',
      dictTag: true,
      dictType: DICT_TYPE.CONTRACTOR_PROJECT_STATUS,
      enableSearch: true,
      searchFormItemProps: {
        component: 'Select',
        options: getIntDictOptions(DICT_TYPE.CONTRACTOR_PROJECT_STATUS)
      }
    },
    {
      label: '开始时间',
      prop: 'startTime'
    },
    {
      label: '结束时间',
      prop: 'endTime'
    },
    {
      label: '项目进度',
      prop: 'projectFinishPercentage',
      slot: 'progress'
    }
  ],
  apiFn: projectListAPI,
  showActions: true,
  actionsColumn: {
    actions: [
      {
        name: 'view',
        text: '查看',
        permission: ['contractor:project:view'],
        onClick({ row }) {
          router.push({
            path: `/contractor-project/detail/${row.id}`
          })
        }
      },
      {
        name: 'edit',
        text: '编辑',
        permission: ['contractor:project:edit'],
        onClick({ row }) {
          projectEditDialogRef.value?.open({ isUpdate: true, row })
        }
      },
      {
        name: 'delete',
        text: '删除',
        type: 'danger',
        permission: ['contractor:project:delete'],
        onClick({ row }) {
          console.log('删除')
          ElMessageBox.confirm('确认执行此操作吗？', '提示！', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            beforeClose: async (action, instance, done) => {
              if (action === 'confirm') {
                instance.confirmButtonLoading = true
                console.log('删除')
                try {
                  await deleteProjectAPI(row.id)
                  ElMessage.success('删除成功')
                  done()
                  basicTableRef.value?.refresh()
                } catch (error) {
                  console.log(error)
                } finally {
                  instance.confirmButtonLoading = false
                }
              } else {
                done()
              }
            }
          }).catch(() => {
            ElMessage.info('已取消')
          })
        }
      }
    ]
  },
  toolbar: [
    {
      name: 'add',
      text: '添加项目',
      icon: 'ep:plus',
      type: 'primary',
      onClick() {
        console.log('添加项目')
        projectEditDialogRef.value?.open()
      }
    }
  ]
}
</script>

<style lang="scss" scoped>
.project {
  .percentage-value {
    font-size: 14px;
  }
}
</style>

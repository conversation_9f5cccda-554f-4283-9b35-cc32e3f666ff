/* 门禁授权接口参数类型 */
// export interface IAuthorisationAPIParams {
//   doorAuthorizeRecordDOs: IDoorAuthorizeRecordDOs[]
// }

/* 门禁权限字段类型 */
export interface IAuthorisationAPIParams {
  createTime?: string
  updateTime?: string
  creator?: string
  updater?: string
  deleted?: boolean
  id?: number
  authorizeIdList: (number | string)[]
  authorizedIdList: (number | string)[]
  type: number | string
  authorizeType: number | string
  startTime?: string
  endTime?: string
  weekTab?: string
  status?: number
  systemType: number
}

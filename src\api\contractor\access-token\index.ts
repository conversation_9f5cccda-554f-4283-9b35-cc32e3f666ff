import http from '@/config/axios'
import type { IQueryAccessTokenAPIParams, IEntAccessTokenInfoData } from './types'

/**
 * @description 出入证记录查询
 * @param { Object } data
 * @param { Number } data.pageNo
 * @param { Number } data.pageSize
 * @param { String } data.username
 * @param { Number } data.projectId
 * @param { Number } data.depId
 * @returns
 */
export function queryAccessTokenAPI(data: IQueryAccessTokenAPIParams) {
  return http.post({
    url: '/contractor/contractorEntryAndExitPermits/page',
    data
  })
}

/**
 * @description 出入证信息录入
 * @param data
 * @returns
 */
export function enteringAccessTokenInfoAPI(data: FormData) {
  return http.post({
    url: '/contractor/contractorEntryAndExitPermits/add',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

<template>
  <div class="reader-table">
    <h3 class="title">{{ title }}</h3>
    <BasicTable ref="basicTableRef" :table-config="tableConfig" />
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue'
import type { IReader } from '../types'
import type { ITableSchema } from '@/components/BasicTable/types'
import { BasicTable, type IBasicTableExpose } from '@/components/BasicTable'

import { useRoute } from 'vue-router'
/* 路由信息对象 */
const route = useRoute()

const type = (route.meta.query as { type: string })?.type
console.log('🚀 ~ type:', type)

const props = defineProps<Partial<ITableSchema>>()

defineOptions({
  name: 'Reader'
})

const emits = defineEmits<{
  (e: 'selection-change', selected: any[]): void
}>()

/* 表格组件实例 */
const basicTableRef = ref<IBasicTableExpose>()
const selectedData = ref<any[]>([])

/* ========== 依赖注入 ========== */
const selectedReader = inject<Ref<IReader[]>>('selectedReader')
/* 表格配置 */
const tableConfig: ITableSchema = {
  columns: props.columns || [],
  tableCardStyle: {
    body: {
      padding: '0'
    }
  },
  align: 'center',
  apiFn: props.apiFn,
  beforeFetch: props.beforeFetch,
  allowSelection: true,
  selectionChange(selected) {
    selectedData.value = selected
    emits('selection-change', selected)
  },
  checkboxProps: {
    reserveSelection: true,
    selectable(row) {
      const disabled = !selectedReader?.value.find((item) => item.id === row.id || item.authorizedId === row.id)
      return disabled
    }
  },
  enableSearch: true,
  searchFormSchema: {
    inline: true,
    showText: false,
    formItems: props.searchFormSchema?.formItems || []
  },
  paginationProps: {
    pageSize: 15,
    pagerCount: 5,
    layout: 'total, prev, pager, next'
  },
  ortherHeight() {
    const titleHeight = 42
    const searchHeight = 69
    const cardPadding = 20
    return titleHeight + searchHeight + cardPadding
  }
}
defineExpose({
  basicTableRef,
  selectedData
})
</script>

<style lang="scss" scoped>
.reader-table {
  height: 100%;

  .title {
    margin-bottom: 10px;
    font-size: 22px;
    font-weight: 400;
    color: #409eff;
  }

  &:deep(.el-pagination) {
    padding-right: 15px;
  }

  .basic-table {
    height: 100%;

    &:deep(.table-search) {
      .table-search__body {
        padding: 10px !important;
      }

      .el-form {
        height: auto;

        .el-form-item {
          margin-right: 8px;
          margin-bottom: 0;
        }
      }
    }

    &:deep(.el-card.table-area) {
      height: calc(100% - 110px);

      .el-card__body {
        height: 100%;

        .el-table {
          display: flex;

          &__inner-wrapper {
            width: 100%;

            &::before {
              width: 0;
            }
          }
        }
      }
    }

    &:deep(.el-input) {
      --el-input-width: 190px;

      min-width: 190px;
    }
  }
}
</style>

<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <BasicForm ref="formRef" :form-schema="formSchema" :form-rules="formRules" />
    <template #footer>
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions, getDictObj } from '@/utils/dict'
import { defaultProps, handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
// import { CommonStatusEnum } from '@/utils/constants'
import { FormRules } from 'element-plus'
import { BasicForm } from '@/components/BasicForm'
import type { IBasicFormExpose, IFormSchema } from '@/components/BasicForm/types'

defineOptions({ name: 'SystemDeptForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

const formRules = reactive<FormRules>({
  parentId: [{ required: true, message: '上级部门不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '部门名称不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }],
  email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
  phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})

const formRef = ref<IBasicFormExpose>() // 表单 Ref
const deptTree = ref<Tree[]>([]) // 树形结构
const userList = ref<UserApi.UserVO[]>([]) // 用户列表

const formSchema: IFormSchema = {
  labelWidth: 80,
  formItems: [
    {
      field: 'parentId',
      label: '上级部门',
      component: 'TreeSelect',
      placeholder: '请选择上级部门',
      componentProps: {
        data: deptTree.value,
        valueKey: 'deptId',
        checkStrictly: true,
        defaultExpandAll: true,
        props: defaultProps
      },
      colProps: {
        span: 24
      }
    },
    {
      field: 'name',
      label: '部门名称',
      component: 'Input',
      placeholder: '请输入部门名称'
    },
    {
      field: 'sort',
      label: '显示排序',
      component: 'InputNumber',
      placeholder: '请输入部门名称',
      defaultValue: 0,
      componentProps: {
        min: 0
      }
    },
    {
      field: 'leaderUserId',
      label: '负责人',
      component: 'Select',
      placeholder: '请选择负责人',
      options: userList.value.map((item) => ({
        label: item.nickname,
        value: item.id
      }))
    },
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      placeholder: '请输入联系电话',
      componentProps: {
        maxlength: 11
      }
    },
    {
      field: 'email',
      label: '邮箱',
      component: 'Input',
      placeholder: '请输入邮箱',
      componentProps: {
        maxlength: 50
      }
    },
    {
      field: 'status',
      label: '状态',
      component: 'Select',
      placeholder: '请选择状态',
      options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
    }
  ]
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  console.log(id)
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const deptInfo = await DeptApi.getDept(id)
      console.log(deptInfo)
      formRef.value?.setFormFieldsValue(deptInfo)
    } finally {
      formLoading.value = false
    }
  }
  // 获得用户列表
  userList.value = await UserApi.getSimpleUserList({
    systemType: 1
  })
  // 获得部门树
  await getTree()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const { isValid, formData } = await formRef.value!.customValidate()

  if (!isValid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = {
      ...(formData as unknown as DeptApi.DeptVO),
      systemType: 1
    }
    console.log('🚀 ~ submitForm ~ data ====> ', data)
    if (formType.value === 'create') {
      await DeptApi.createDept(data)
      message.success(t('common.createSuccess'))
    } else {
      await DeptApi.updateDept(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 获得部门树 */
const getTree = async () => {
  const data = await DeptApi.getSimpleDeptList({
    systemType: getDictObj(DICT_TYPE.SYSTEM_TYPE, 1)?.value
  })
  let dept: Tree = { id: 0, name: '顶级部门', children: [] }
  dept.children = handleTree(data)
  !deptTree.value.length && deptTree.value.push(dept)
}
</script>

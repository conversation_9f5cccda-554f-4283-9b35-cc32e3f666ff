<script lang="ts">
import type { IFormSchema, ICascaderOption, IOption } from '../types'
import { COMPONENT_MAP, getComponentPlaceholder } from './render-utils'
import { ElCol, ElForm, ElFormItem, ElRow } from 'element-plus'

export default defineComponent({
  name: 'BasicForm',
  props: {
    formSchema: {
      type: Object as PropType<IFormSchema>
    }
  },
  setup(props, context) {
    /** 当前组件实例 */
    const currentInstance = getCurrentInstance()
    /* 表单收集的数据 */
    const formData = ref<Record<string, any>>({})
    /** 是否禁用所有表单项 */
    const disabled = ref(false)
    /** 选择器选择select/cascader */
    const options = ref<Record<string, IOption[] | ICascaderOption[]>>({})
    /** el-form组件实例对象 */
    const formInstance = ref()
    /** 排除 ElformItem 组件的属性 */
    const propsExcludeKeys = [
      'label',
      'disabled',
      'labelWidth',
      'modelValue',
      'rules',
      'colProps',
      'component',
      'options',
      'componentProps'
    ]

    /* 获取级联选择器的选项 */
    const getFormItemOptions = () => {
      console.log('getFormItemOptions')
      props.formSchema?.formItems?.forEach((item) => {
        if (item.options) {
          if (typeof item.options === 'function') {
            if (item.component === 'Cascader') {
              item
                .options()
                .then((res) => {
                  options.value[item.field] = structureCasOptions(res)
                })
                .catch((error) => {
                  console.error(error)
                })
            } else if (item.component === 'Select') {
              item.options().then((res: any[] | { list: any[] }) => {
                const dataList = (res as any[]).length ? res : (res as { list: any[] }).list
                options.value[item.field] = (dataList as any[]).map((data) => {
                  if (item.componentProps?.optionsLabelField && item.componentProps?.optionsValueField) {
                    return {
                      label: data[item.componentProps?.optionsLabelField],
                      value: data[item.componentProps?.optionsValueField]
                    }
                  } else {
                    if (item.componentProps?.optionsLabelField) {
                      return {
                        label: data[item.componentProps?.optionsLabelField],
                        value: data.id
                      }
                    } else if (item.componentProps?.optionsValueField) {
                      return {
                        label: data[item.field],
                        value: data[item.componentProps?.optionsValueField]
                      }
                    } else {
                      return {
                        label: data[item.field],
                        value: data.id
                      }
                    }
                  }
                })
              })
            }
          } else if (Array.isArray(item.options)) {
            console.log('🚀 ~ props.formSchema?.formItems?.forEach ~ item.options ====> ', item.field, item.options)
            options.value[item.field] = item.options
          }
        }
      })
    }

    /* 将列表数据构造cascaderoptions所需要的树结构数据 */
    function structureCasOptions(arr: any[], parentId = 0) {
      let options: ICascaderOption[] = []

      for (const item of arr) {
        const option: ICascaderOption = {
          label: item.name,
          value: item.id,
          children: []
        }

        if (item.parentId === parentId) {
          let children = structureCasOptions(arr, item.id)
          if (children.length) {
            option.children = children
          }
          options.push(option)
        }
      }

      return options
    }

    watch(
      () => props.formSchema?.formItems,
      () => {
        getFormItemOptions()
      },
      {
        deep: true,
        immediate: true
      }
    )

    /* 设置表单字段得默认值 */
    const setFormFieldsDefaultValue = () => {
      props.formSchema?.formItems?.forEach((item) => {
        if (item.defaultValue !== undefined) {
          formData.value[item.field] = item.defaultValue
        }
      })
    }

    setFormFieldsDefaultValue()

    /* 设置表单字段的值 */
    const setFormFieldsValue = (data: Record<string, any>) => {
      console.log(data)
      Object.keys(data).forEach((key) => {
        formData.value[key] = data[key]
      })
    }

    /** 禁用表单项 */
    const disabledFormFields = () => {
      disabled.value = true
    }

    const customValidate = async <R extends Record<string, any>>(
      cb?: (isValid: boolean, formData) => void
    ): Promise<{ isValid: boolean; formData: R } | void> => {
      if (!formInstance.value) return
      const isValid = await formInstance.value.validate()
      cb && cb(isValid, formData.value)
      return {
        isValid,
        formData: formData.value as R
      }
    }

    /** 绑定ElForm组件实例,组件更新/卸载时执行 */

    const expose = {
      formData,
      customValidate,
      setFormFieldsValue,
      disabledFormFields
    }

    const changeRef = (instance) => {
      if (!currentInstance || !instance) return
      formInstance.value = instance
      currentInstance.exposeProxy = currentInstance.exposed = instance

      /**
       * @override 重写表单校验方法
       * @param cb 校验回调函数
       */
      Object.keys(expose).forEach((key) => {
        if (currentInstance.exposed && currentInstance.exposeProxy) {
          currentInstance.exposed[key] = currentInstance.exposed[key] = expose[key]
        }
      })
    }

    /**
     * formItem
     */
    function formItemRender() {
      return props.formSchema?.formItems?.map((item) => {
        if (item.hidden) return null
        /** 表单项的属性 */
        const formItemComponentProps = { ...item, ...item.componentProps }
        const excludedProps = Object.keys(formItemComponentProps).reduce((obj, key) => {
          if (!propsExcludeKeys.includes(key)) {
            obj[key] = formItemComponentProps[key]
          }
          return obj
        }, {})
        /** el-col span 属性 */
        const span =
          item.colProps?.span !== undefined
            ? item.colProps.span
            : props.formSchema?.colProps?.span !== undefined
              ? props.formSchema?.colProps?.span
              : 12

        /** 默认为 el-input 组件 */
        const componentName = item.component ?? 'Input'

        /** label */
        const label = typeof item.label === 'function' ? item.label(formData.value) : item.label

        /** 表单项组件 */
        return h(
          ElCol,
          { span: span as unknown as number },
          {
            default: () =>
              h(
                ElFormItem,
                {
                  label: !item.hiddenLabel ? label : '',
                  labelWidth: item.labelWidth,
                  prop: item.field,
                  rules: item.rules
                },
                {
                  default: () => {
                    if (item.slot) {
                      return context.slots[item.slot]?.({ formData: formData.value, field: item.field })
                    } else {
                      console.log(item.field, options.value[item.field])

                      const disabled =
                        typeof item.disabled === 'function' ? item.disabled(formData.value) : item.disabled

                      return h(COMPONENT_MAP[componentName], {
                        placeholder: getComponentPlaceholder(componentName, label as string),
                        ...excludedProps,
                        disabled,
                        clearable: true,
                        formData: formData.value,
                        modelValue: formData.value[item.field],
                        'onUpdate:modelValue': (value) => (formData.value[item.field] = value),
                        options: options.value[item.field]
                      })
                    }
                  }
                }
              )
          }
        )
      })
    }

    /**
     * 表单
     */
    return function render() {
      return h(
        ElForm,
        {
          ref: changeRef,
          model: formData.value,
          rules: props.formSchema?.rules,
          labelWidth: props.formSchema?.labelWidth
        },
        () =>
          h(
            ElRow,
            {},
            {
              default: () => [formItemRender(), context.slots.col && context.slots.col()]
            }
          )
      )
    }
  }
})
</script>

<style lang="scss" scoped>
.el-form {
  .el-form-item {
    // width: 100%;
    margin-right: 15px;

    &:deep(.el-cascader) {
      width: 100%;

      .el-input {
        --el-input-width: 100%;

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          appearance: none !important;
        }

        input[type='number'] {
          appearance: textfield !important;
        }
      }
    }

    &:deep(.el-cascader),
    &:deep(.el-input),
    &:deep(.el-select),
    &:deep(.el-date-editor) {
      min-width: 220px;
    }

    &:deep(.el-input-number) {
      .el-input {
        --el-input-width: auto;

        min-width: auto;
      }
    }

    &:deep(.el-date-editor) {
      --el-date-editor-width: 100%;
    }

    .el-input {
      &:deep(.el-input__inner) {
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          appearance: none !important;
        }

        &[type='number'] {
          appearance: textfield;
        }
      }
    }
  }

  .avatar-uploader {
    &:deep(.el-upload) {
      position: relative;
      overflow: hidden;
      cursor: pointer;
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      transition: var(--el-transition-duration-fast);
    }

    &:deep(.el-upload):hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      width: 178px;
      height: 178px;
      font-size: 28px;
      color: #8c939d;
      text-align: center;
    }
  }
}
</style>

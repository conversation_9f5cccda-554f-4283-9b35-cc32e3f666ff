<template>
  <div class="monitoring-door">
    <div class="content">
      <div class="door-list">
        <CardList ref="cardListRef" :list-config="listConfig">
          <template #desc="{ data }">
            <div class="desc">
              <div>开门延迟：{{ data.doorOpeningDelay }}ms</div>
              <div>工作模式：{{ getDictLabel(DICT_TYPE.DOOR_WORK_TYPE, data.workType) }}</div>
              <div>关门延迟：{{ data.closedDoorDelay }}ms</div>
            </div>
          </template>
        </CardList>
      </div>
      <!-- 实时日志 -->
      <div class="real-log">
        <h2 class="title">实时日志</h2>
        <RealLog ref="realLogRef" :height="doorListHeight" />
      </div>
    </div>
    <!-- 门信息编辑对话框 -->
    <DoorInfoEditDialog ref="dialogRef" @submit:success="cardListRef?.refresh" />
  </div>
</template>

<script setup lang="ts">
import DoorInfoEditDialog from './components/DoorInfoEditDialog.vue'
import RealLog from './components/log/index.vue'
import { CardList, type ICardListConf } from '@/components/CardList'
import {
  controllerDoorInfoListAPI,
  controllerDoorInfoDeleteAPI,
  controllerDoorInfoOpenAPI
} from '@/api/real-time-monitoring/door'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { ElMessageBox } from 'element-plus'
import { useMessage } from '@/hooks/web/useMessage'

defineOptions({
  name: 'MonitoringDoor'
})

const message = useMessage()

/* 对话框组件实例 */
const dialogRef = ref<InstanceType<typeof DoorInfoEditDialog>>()
/** 卡片列表组件实例 */
const cardListRef = ref<InstanceType<typeof CardList>>()
/* 实时日志表格组件实例 */
const realLogRef = ref<InstanceType<typeof RealLog>>()
/** 门列表高度 */
const doorListHeight = ref(0)

const listConfig: ICardListConf = {
  cardItemField: {
    title: {
      label: '名称',
      prop: 'doorName'
    },
    desc: {
      label: '控制器编号',
      prop: 'controllerNumber'
    },
    status: {
      label: '门锁状态',
      prop: 'doorLockStatus',
      dictType: DICT_TYPE.DOOR_LOCK_STATUS
    },
    image: (data) => {
      let img = ''
      switch (data.workType) {
        case 0:
          img = new URL('../../../assets/imgs/door_open.png', import.meta.url).href
          break
        default:
          img = new URL('../../../assets/imgs/door_closed.png', import.meta.url).href
          break
      }

      return img
    }
  },
  apiFn: controllerDoorInfoListAPI,

  actions: [
    {
      name: 'edit',
      text: '编辑',
      onClick(data) {
        console.log(import.meta.url)
        console.log(new URL('../../../assets/imgs/door_closed.png', import.meta.url))
        dialogRef.value?.open({
          title: '编辑门信息',
          isUpdate: true,
          row: data
        })
      }
    },
    {
      name: 'open',
      text: '开门',
      onClick(data) {
        ElMessageBox.confirm('确定下发开门指令吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              try {
                await controllerDoorInfoOpenAPI(data.id, '21')
                message.success('下发指令成功')
                cardListRef.value?.refresh()
                done()
              } catch (error) {
                console.log(error)
              } finally {
                instance.confirmButtonLoading = false
              }
            } else {
              done()
              message.info('取消')
            }
          }
        }).catch((error) => {
          console.log(error)
        })
      }
    },
    {
      name: 'delete',
      text: '删除',
      onClick(data) {
        ElMessageBox.confirm('是否删除该门信息', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            console.log('删除', data)
            try {
              await controllerDoorInfoDeleteAPI(data.id)
              ElMessage.success('删除成功')
              cardListRef.value?.refresh()
            } catch (error) {
              console.log(error)
            }
          })
          .catch(() => {
            ElMessage.info('取消')
          })
      }
    }
  ]
}
</script>

<style lang="scss" scoped>
.monitoring-door {
  height: calc(100vh - var(--top-tool-height) - var(--tags-view-height) - var(--app-footer-height) - 10px) !important;

  .content {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100%;

    .door-list,
    .real-log {
      flex: 1 0 50%;
      overflow: hidden;
    }

    .real-log {
      .title {
        margin-bottom: 10px;
        font-size: 20px;
        color: #409eff;
      }
    }

    .door-list {
      overflow: auto;

      .desc {
        display: flex;
        margin-top: 10px;
        justify-content: space-between;
        flex-wrap: wrap;
        row-gap: 10px;
      }
    }
  }
}
</style>

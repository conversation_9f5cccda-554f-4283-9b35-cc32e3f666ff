<script lang="ts">
import { type PropType, getCurrentInstance } from 'vue'
import type { ITableSearchForm, ITableSchema } from '../../types'
import { Search, Refresh } from '@element-plus/icons-vue'
import { BaseForm, BasicForm } from '@/components/BasicForm'
import { ContentWrap } from '@/components/ContentWrap'
import { ElButton } from 'element-plus'
import { Icon } from '@/components/Icon'

/** 表格头部 */
export default defineComponent({
  components: {
    Icon,
    ElButton,
    BaseForm,
    BasicForm,
    ContentWrap
  },
  props: {
    cardStyle: {
      type: Object as PropType<ITableSchema['searchCardStyle']>
    },
    searchFormItems: {
      type: Object as PropType<ITableSearchForm['formItems']>
    },
    formSchema: {
      type: Object as PropType<ITableSearchForm>,
      default: () => ({})
    }
  },
  emits: ['search', 'reset'],
  setup(props, { emit }) {
    const currentInstance = getCurrentInstance()
    /** 表单组件实例 */
    const formRef = ref<InstanceType<typeof BaseForm>>()

    /** 是否展示搜索区域 */
    const expansionSearch = ref(true)

    /** 所有的表单项DOM对象 */
    const allFormItemDOM = computed(() => {
      if (!formRef.value) return []
      return Array.from(formRef.value?.$el.querySelectorAll('.el-form-item')) as HTMLElement[]
    })

    /** 所有form-item项的宽度和 */
    const itemsWidth = computed(() => {
      if (!allFormItemDOM.value) return 0
      // const items = Array.from(formRef.value?.$el.querySelectorAll('.el-form-item'))
      return allFormItemDOM.value.reduce<number>((result: number, item: HTMLElement) => {
        return result + item.clientWidth
      }, 0)
    })

    /** 表单的宽度 */
    const formWidth = computed(() => formRef.value?.$el.clientWidth)

    /** 是否显示搜索的箭头 */
    const showArrow = computed(() => {
      return itemsWidth?.value > formWidth.value
    })

    /** 一行能展示的下的表单项个数 */
    const onlyOneRawDisplayCount = computed(() => {
      let containerWidth = formWidth.value
      // const items: HTMLElement[] = Array.from(formRef.value?.$el.querySelectorAll('.el-form-item'))
      const count = allFormItemDOM.value?.reduce((count, item) => {
        if (containerWidth >= item.clientWidth) {
          containerWidth -= item.clientWidth
          count++
        }

        return count
      }, 0)
      return Math.max(0, Math.ceil(count) - 1)
    })

    /** 监听是否展开expansionSearch的值 */
    watch(expansionSearch, (newVal) => {
      console.log('onlyOneRawDisplayCount ====>', onlyOneRawDisplayCount.value)
      console.log('props.formSchema', props.formSchema)
      /** 改变表单项的显示状态 */
      if (showArrow.value) {
        props.searchFormItems?.forEach((item, index) => {
          if (index >= onlyOneRawDisplayCount.value) {
            item.hidden = !newVal
          }
        })
      }
    })

    /* 搜索 */
    const handleSearch = () => {
      emit('search', formRef.value?.formData)
    }

    /* 重置 */
    const handleReset = () => {
      formRef.value?.resetFields()
      emit('reset', formRef.value?.formData)
    }

    /** 搜索区域展开/收起 */
    const handleToggleSearchExpan = () => {
      expansionSearch.value = !expansionSearch.value
    }

    onMounted(() => {
      /** 为了触发watch，开启immedate的话会报错，dom还没加载 */
      expansionSearch.value = false
    })

    /** 向外暴露 */
    function changeRef(formInstance) {
      if (!currentInstance) return
      currentInstance.exposeProxy = currentInstance.exposed = formRef.value = formInstance
    }

    /** 展开搜索区域箭头 */
    const arrowRender = () => {
      /** 是否显示 */
      const toolTipContent = expansionSearch.value ? '收起' : '展开'
      return showArrow.value
        ? h('div', { class: 'expan', onClick: handleToggleSearchExpan }, [
            toolTipContent,
            h(Icon, {
              icon: 'ep:arrow-down',
              size: 14,
              class: ['expan-arrow', expansionSearch.value && 'active']
            })
          ])
        : null
    }

    return () => {
      /** 表格搜索区域搜索&重置按钮 */
      const showText = props.formSchema?.showText ?? true
      const searchBtn = !props.formSchema?.hiddenSearch
        ? showText
          ? h(ElButton, { type: 'primary', icon: Search, onClick: handleSearch }, () => '搜索')
          : h(ElButton, { type: 'primary', icon: Search, onClick: handleSearch })
        : null
      const resetBtn = !props.formSchema?.hiddenReset
        ? showText
          ? h(ElButton, { icon: Refresh, onClick: handleReset }, () => '重置')
          : h(ElButton, { icon: Refresh, onClick: handleReset })
        : null

      /** card-body 卡片的样式 */
      const bodyStyle = props.cardStyle?.body
      const noBorder = props.cardStyle?.showBorder === false ? 'not-border' : null

      const formSchema = {
        inline: true,
        formItems: props.searchFormItems,
        colProps: {
          span: 25
        }
      } as ITableSearchForm

      console.log('🚀 ~ return ~ formSchema ====> ', formSchema)

      return h(
        ContentWrap,
        {
          class: ['table-search', noBorder],
          bodyStyle,
          bodyClass: 'table-search__body'
        },
        {
          default: () => [
            h(
              BasicForm,
              { ref: changeRef, formSchema },
              {
                col: () => h('div', { class: 'table-search__btn-group' }, [searchBtn, resetBtn, arrowRender()])
              }
            )
          ]
        }
      )
    }
  }
})
</script>

<style lang="scss" scoped>
.table {
  &-search {
    &:deep(.table-search__body) {
      position: relative;
    }

    &:deep(.el-form) {
      .el-row {
        position: static;
      }
    }

    &:deep(.table-search__body) {
      padding: 18px 10px 0 !important;
    }

    &__btn-group {
      display: flex;
      font-size: 14px;

      .expan {
        display: flex;
        padding-top: 5px;
        margin-left: 10px;
        color: var(--el-color-primary);
        cursor: pointer;
      }

      .expan-arrow {
        transform: translateY(3px);
        transition: transform 0.3s;

        &.active {
          transform: translateY(3px) rotate(180deg);
        }
      }
    }
  }
}
</style>

<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" class="!w-450px">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="80px">
      <!-- <el-form-item label="设备编号" prop="equipmentNumber">
        <el-input v-model="formData.equipmentNumber" placeholder="请输入设备编号" clearable/>
      </el-form-item> -->
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input v-model="formData.equipmentName" placeholder="请输入设备名称" />
      </el-form-item>
      <el-form-item label="设备IP" prop="equipmentIp">
        <el-input v-model="formData.equipmentIp" placeholder="请输入设备IP" clearable />
      </el-form-item>
      <el-form-item label="设备端口" prop="equipmentPort">
        <el-input v-model.number="formData.equipmentPort" placeholder="请输入设备端口" clearable />
      </el-form-item>
      <el-form-item label="安装位置" prop="installationPosition">
        <el-input v-model="formData.installationPosition" placeholder="请输入安装位置" clearable />
      </el-form-item>
      <el-form-item label="进/出" prop="type">
        <el-select v-model="formData.type" clearable placeholder="请选择类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.HARDWARE_CAR_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设备类型" prop="companyType">
        <el-select v-model="formData.companyType" clearable placeholder="请选择设备类型">
          <el-option v-for="dict of company" :key="dict.id" :label="dict.company" :value="dict.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="账号" prop="accountNumber">
        <el-input v-model="formData.accountNumber" placeholder="请输入账号" clearable />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="formData.password" placeholder="请输入密码" clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" :loading="formLoading" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as HardwareApi from '@/api/hardware'

defineOptions({ name: 'HardwareCarForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const company = ref([]) //车牌识别一体机厂家列表
const formData = ref({
  id: undefined,
  // equipmentNumber: '',
  equipmentName: '',
  installationPosition: '',
  equipmentIp: '',
  equipmentPort: undefined,
  type: undefined,
  companyType: undefined,
  accountNumber: '',
  password: ''
})

const formRules = reactive({
  // equipmentNumber: [
  //   { required: true, message: '设备编号不能为空', trigger: 'change' },
  //   {
  //     validator: (rule, value, callback) => {
  //       if (!/^[A-Za-z0-9]*$/.test(value)) {
  //         callback(new Error('密码只能包含数字或者字母'));
  //         return;
  //       }
  //       callback();
  //     },
  //     trigger: 'change'
  //   }
  // ],
  equipmentName: [{ required: true, message: '设备型号不能为空', trigger: 'change' }],
  installationPosition: [{ required: true, message: '安装位置不能为空', trigger: 'change' }],
  equipmentIp: [
    { required: true, message: '设备IP不能为空', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        const ipRegex =
          /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]?|0)\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]?|0)$/
        if (!value) {
          callback(new Error('设备IP不能为空'))
        } else if (!ipRegex.test(value)) {
          callback(new Error('请输入有效的IP地址'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  equipmentPort: [
    { required: true, message: '设备端口不能为空', trigger: 'change' },
    { type: 'number', min: 1, max: 65535, message: '输入值必须介于1和65535之间', trigger: 'change' }
  ],
  type: [{ required: true, message: '设备类型不能为空', trigger: 'change' }],
  companyType: [{ required: true, message: '设备类型不能为空', trigger: 'change' }],
  accountNumber: [
    { required: true, message: '账号不能为空', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (!/^[A-Za-z0-9]*$/.test(value)) {
          callback(new Error('账号只能包含数字或者字母'))
          return
        }
        callback()
      },
      trigger: 'change'
    }
  ],
  password: [
    { required: true, message: '密码不能为空', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (!/^[A-Za-z0-9]*$/.test(value)) {
          callback(new Error('密码只能包含数字或者字母'))
          return
        }

        callback()
      },
      trigger: 'change'
    }
  ]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  getFacilityCompany()
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await HardwareApi.getFaceOrCarRecognition(id)
    } finally {
      formLoading.value = false
    }
  }
}
/**获取车牌一体识别机厂级 */
const getFacilityCompany = async () => {
  const data = await HardwareApi.getFacilityCompany(5)
  company.value = data
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    // equipmentNumber: '',
    equipmentName: '',
    installationPosition: '',
    equipmentIp: '',
    equipmentPort: undefined,
    type: undefined,
    companyType: undefined,
    accountNumber: '',
    password: ''
  }
  formRef.value?.resetFields()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as HardwareApi.updateFaceOrCarData
    if (formType.value === 'create') {
      await HardwareApi.addFaceOrCarRecognition(data)
      console.log(data)
      message.success(t('common.createSuccess'))
    } else {
      await HardwareApi.updateFaceOrCarRecognition(data)

      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>

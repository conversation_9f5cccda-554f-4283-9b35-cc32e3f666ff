<template>
  <div class="role-smartlock-tab">
    <div class="tools">
      <el-checkbox
        label="全选"
        border
        :indeterminate="isIndeterminate"
        :disabled="checkboxDisabled"
        v-model="checkedAll"
        @change="handleCheckAllChange"
      />
    </div>
    <content-wrap>
      <el-checkbox-group v-model="selectedSmartlock" @change="handleCheckChange">
        <el-checkbox
          v-for="smartlock in facilities"
          :key="smartlock.id"
          :label="smartlock.deviceName"
          :value="smartlock.id"
          :disabled="smartlock.status === 1"
          v-loading="smartlockLoading"
        >
          {{ smartlock.deviceName }}
        </el-checkbox>
      </el-checkbox-group>
    </content-wrap>
  </div>
</template>
<script lang="ts" setup>
// import * as FacilityApi from '@/api/system/facility'
import * as PermissionApi from '@/api/system/permission'

const props = defineProps<{
  roleId: number
  facilities: any[]
}>()

const message = useMessage() // 消息弹窗

// const smartlockOptions = ref<any[]>([]) // 智能锁资源结构
/* 全选 */
const checkedAll = ref(false)
/* 是否禁用全选复选框 */
const checkboxDisabled = computed(() => {
  console.log(props.facilities.every((item) => item.status === 1))
  return props.facilities.every((item) => item.status === 1)
})
/* 半选状态 */
const isIndeterminate = ref(false)
/* 可选的节点 */
const allowCheckeNodes = ref(props.facilities.filter((item) => item.status !== 1))
const selectedSmartlock = ref<number[]>([])
const smartlockLoading = ref(false) // 智能锁资源的加载中：1）修改时的数据加载；
// const facilityList = ref([]) //所有设备资源
const isSmartlockUpdateStatus = ref(false) //默认智能锁标签页数据无修改
const smartlockType = ref(3) //智能锁资源类型3

const smartlockData = ref({
  id: undefined,
  type: undefined,
  resourceIdList: []
})

const smartlockTabLoading = async () => {
  smartlockLoading.value = true
  try {
    smartlockData.value.resourceIdList = await PermissionApi.getRoleDoorList(props.roleId, smartlockType.value)
    checkAllBoxStatusChange(smartlockData.value.resourceIdList)
    selectedSmartlock.value = smartlockData.value.resourceIdList
    isSmartlockUpdateStatus.value = false
  } finally {
    smartlockLoading.value = false
  }
}

/* 全选复选框值变化事件处理函数 */
const handleCheckAllChange = (val: boolean) => {
  if (val) {
    const checkeds = allowCheckeNodes.value.map((item) => item.id)
    /* 取消半选，更改为全选 */
    isIndeterminate.value = false
    selectedSmartlock.value = checkeds
  } else {
    /* 取消半选 */
    isIndeterminate.value = false
    selectedSmartlock.value = []
  }
  console.log(selectedSmartlock.value)
  isSmartlockUpdateStatus.value = !arraysEqual(smartlockData.value.resourceIdList, selectedSmartlock.value)
  console.log(isSmartlockUpdateStatus.value)
}

/* 全选复选框状态变化 */
const checkAllBoxStatusChange = (checkedKeys) => {
  if (checkedKeys.length !== 0) {
    // const allowIncludesCheckeds = checkedKeys.every((key) => allowCheckeNodes.value.some((allow) => allow.id === key))

    // if (allowIncludesCheckeds) {
    if (checkedKeys.length < allowCheckeNodes.value.length) {
      /* 取消全选，更改为半选 */
      isIndeterminate.value = true
      checkedAll.value = false
    } else {
      isIndeterminate.value = false
      checkedAll.value = true
    }
    // }
  } else {
    /* 取消全选、半选 */
    checkedAll.value = false
    isIndeterminate.value = false
  }
}

onMounted(() => {
  smartlockTabLoading()
})

//提交智能锁资源数据
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitRoleSmartlockPermissions = async (roleId: number) => {
  console.log(isSmartlockUpdateStatus.value)
  if (!isSmartlockUpdateStatus.value) return

  try {
    smartlockLoading.value = true
    const data = {
      roleId: roleId,
      type: 3,
      resourceIdList: selectedSmartlock.value
    }
    await PermissionApi.assignRoleFacility(data)
    message.success('智能锁资源配置成功')
    // 发送操作成功的事件
    emit('success')
    isSmartlockUpdateStatus.value = false
  } finally {
    smartlockLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  // 重置选项
  // doorUpdateStatus.value=0           // 数据脏标记
  // 重置表单
  smartlockData.value = {
    id: undefined,
    type: undefined,
    resourceIdList: []
  }
}

const handleCheckChange = () => {
  const hasChanged = !arraysEqual(smartlockData.value.resourceIdList, selectedSmartlock.value)
  isSmartlockUpdateStatus.value = hasChanged
  console.log(isSmartlockUpdateStatus.value)
  checkAllBoxStatusChange(selectedSmartlock.value)
  // if (arraysEqual(smartlockData.resourceIdList, selectedSmartlockOptions.value)) {
  //   faceUpdateStatus.value = 0
  // } else {
  //   faceUpdateStatus.value = 2
  // }
}

// 数组对比（只比较内容 忽略顺序）
function arraysEqual(arr1, arr2) {
  if (arr1.length !== arr2.length) {
    return false
  }
  const set1 = new Set(arr1)
  const set2 = new Set(arr2)
  return [...set1].every((value) => set2.has(value)) && [...set2].every((value) => set1.has(value))
}

defineExpose({
  // smartlockTabLoading,
  resetForm,
  submitRoleSmartlockPermissions,
  isSmartlockUpdateStatus
}) // 提供 submitRoleSmartlockPermissions 方法，用于提交智能锁标签页权限数据
</script>

<style lang="scss" scoped>
.tools {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
</style>
